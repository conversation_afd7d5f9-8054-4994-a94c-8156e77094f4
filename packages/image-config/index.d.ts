export interface RemotePattern {
  protocol?: 'http' | 'https';
  hostname: string;
  port?: string;
  pathname?: string;
}

export interface ImageOptimizationOptions {
  enableOptimization?: boolean;
  deviceSizes?: number[];
  imageSizes?: number[];
  minimumCacheTTL?: number;
  formats?: string[];
}

export interface ImageLoaderOptions {
  loaderType?: 'default' | 'cloudinary' | 'akamai' | 'custom';
  customLoaderPath?: string;
}

export interface ImageConfigOptions extends ImageOptimizationOptions, ImageLoaderOptions {
  includeGoogleMaps?: boolean;
  includePexels?: boolean;
  additionalPatterns?: RemotePattern[];
}

export interface NextImageConfig {
  unoptimized?: boolean;
  domains?: string[];
  remotePatterns?: RemotePattern[];
  deviceSizes?: number[];
  imageSizes?: number[];
  formats?: string[];
  minimumCacheTTL?: number;
  loader?: string;
  loaderFile?: string;
}

/**
 * Get image domains configuration based on environment
 */
export function getImageDomains(): string[];

/**
 * Get remote patterns configuration based on environment and app type
 */
export function getImageRemotePatterns(options?: {
  includeGoogleMaps?: boolean;
  includePexels?: boolean;
  additionalPatterns?: RemotePattern[];
}): RemotePattern[];

/**
 * Get complete image configuration for Next.js
 */
export function getImageConfig(options?: {
  includeGoogleMaps?: boolean;
  includePexels?: boolean;
  additionalPatterns?: RemotePattern[];
  unoptimized?: boolean;
}): NextImageConfig;

/**
 * Get image optimization settings based on environment
 */
export function getImageOptimization(options?: ImageOptimizationOptions): Partial<NextImageConfig>;

/**
 * Get loader configuration for different CDNs
 */
export function getImageLoader(loaderType?: string, customLoaderPath?: string): Partial<NextImageConfig>;

/**
 * Get complete image optimization configuration
 */
export function getCompleteImageOptimization(options?: ImageOptimizationOptions & ImageLoaderOptions): Partial<NextImageConfig>;

/**
 * Create a complete Next.js image configuration
 */
export function createImageConfig(options?: ImageConfigOptions): NextImageConfig;

/**
 * Preset configurations for different apps
 */
export const presets: {
  main(): NextImageConfig;
  gust(): NextImageConfig;
  production(appOptions?: ImageConfigOptions): NextImageConfig;
  development(appOptions?: ImageConfigOptions): NextImageConfig;
};

export default {
  createImageConfig,
  presets,
  getImageDomains,
  getImageRemotePatterns,
  getImageConfig,
  getImageOptimization,
  getImageLoader,
  getCompleteImageOptimization,
};
