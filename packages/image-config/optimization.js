/**
 * Image optimization configuration for different environments
 */

/**
 * Get image optimization settings based on environment
 * @param {Object} options - Optimization options
 * @param {boolean} options.enableOptimization - Whether to enable image optimization
 * @param {Array} options.deviceSizes - Device sizes for responsive images
 * @param {Array} options.imageSizes - Image sizes for responsive images
 * @param {number} options.minimumCacheTTL - Minimum cache TTL in seconds
 * @param {Array} options.formats - Supported image formats
 * @returns {Object} Image optimization configuration
 */
function getImageOptimization(options = {}) {
  const {
    enableOptimization = process.env.NODE_ENV === 'production',
    deviceSizes = [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes = [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL = 60,
    formats = ['image/webp'],
  } = options;

  const config = {
    deviceSizes,
    imageSizes,
    formats,
    minimumCacheTTL,
  };

  // Disable optimization if requested or in development
  if (!enableOptimization) {
    config.unoptimized = true;
  }

  return config;
}

/**
 * Get loader configuration for different CDNs
 * @param {string} loaderType - Type of loader ('default', 'cloudinary', 'akamai', 'custom')
 * @param {string} customLoaderPath - Path to custom loader function
 * @returns {Object} Loader configuration
 */
function getImageLoader(loaderType = 'default', customLoaderPath = null) {
  const config = {};

  switch (loaderType) {
    case 'cloudinary':
      config.loader = 'cloudinary';
      break;
    case 'akamai':
      config.loader = 'akamai';
      break;
    case 'custom':
      if (customLoaderPath) {
        config.loader = 'custom';
        config.loaderFile = customLoaderPath;
      }
      break;
    default:
      config.loader = 'default';
  }

  return config;
}

/**
 * Get complete image optimization configuration
 * @param {Object} options - All optimization options
 * @returns {Object} Complete optimization configuration
 */
function getCompleteImageOptimization(options = {}) {
  const optimization = getImageOptimization(options);
  const loader = getImageLoader(options.loaderType, options.customLoaderPath);

  return {
    ...optimization,
    ...loader,
  };
}

module.exports = {
  getImageOptimization,
  getImageLoader,
  getCompleteImageOptimization,
};
