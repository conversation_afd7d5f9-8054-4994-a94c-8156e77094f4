# @rnt/image-config

Shared image configuration package for Next.js applications in the Rentoor monorepo.

## Features

- Environment-based domain configuration
- Automatic staging URL handling for development
- App-specific presets (main, gust)
- Image optimization settings
- TypeScript support
- Consistent configuration across all apps

## Installation

This package is part of the monorepo and should be installed automatically via workspace dependencies.

## Usage

### Basic Usage

```javascript
const { presets } = require('@rnt/image-config');

// In your next.config.js
const nextConfig = {
  images: presets.main(), // or presets.gust()
};
```

### Custom Configuration

```javascript
const { createImageConfig } = require('@rnt/image-config');

const nextConfig = {
  images: createImageConfig({
    includeGoogleMaps: true,
    includePexels: true,
    enableOptimization: true,
    additionalPatterns: [
      {
        protocol: 'https',
        hostname: 'example.com',
      },
    ],
  }),
};
```

### Environment-Based Configuration

The package automatically handles environment-based configuration:

- **Development**: Includes `stg-v3.rentoor.com` for staging access
- **Production**: Excludes staging URLs for security

### Available Presets

#### Main App
```javascript
const { presets } = require('@rnt/image-config');
const config = presets.main();
```

#### Gust App
```javascript
const { presets } = require('@rnt/image-config');
const config = presets.gust(); // Includes Google Maps and Pexels
```

#### Production Optimized
```javascript
const { presets } = require('@rnt/image-config');
const config = presets.production({
  includeGoogleMaps: true,
  includePexels: true,
});
```

#### Development
```javascript
const { presets } = require('@rnt/image-config');
const config = presets.development({
  includeGoogleMaps: true,
});
```

## API Reference

### Functions

- `createImageConfig(options)` - Create custom image configuration
- `getImageDomains()` - Get environment-based domains array
- `getImageRemotePatterns(options)` - Get remote patterns configuration
- `getImageConfig(options)` - Get basic image configuration
- `getImageOptimization(options)` - Get optimization settings
- `presets.main()` - Main app preset
- `presets.gust()` - Gust app preset
- `presets.production(options)` - Production preset
- `presets.development(options)` - Development preset

### Options

```typescript
interface ImageConfigOptions {
  includeGoogleMaps?: boolean;
  includePexels?: boolean;
  additionalPatterns?: RemotePattern[];
  enableOptimization?: boolean;
  deviceSizes?: number[];
  imageSizes?: number[];
  minimumCacheTTL?: number;
  formats?: string[];
  loaderType?: 'default' | 'cloudinary' | 'akamai' | 'custom';
  customLoaderPath?: string;
}
```

## Migration Guide

### From Hardcoded Configuration

**Before:**
```javascript
// next.config.js
const nextConfig = {
  images: {
    unoptimized: true,
    domains: [
      'axnqxdrfqrex.compat.objectstorage.me-jeddah-1.oraclecloud.com',
      'rentoor-bucket.s3.eu-central-1.amazonaws.com',
      'stg-v3.rentoor.com', // This was hardcoded!
      'localhost',
    ],
    remotePatterns: [
      // ... hardcoded patterns
    ],
  },
};
```

**After:**
```javascript
// next.config.js
const { presets } = require('@rnt/image-config');

const nextConfig = {
  images: presets.main(), // or presets.gust()
};
```

## Benefits

1. **Environment Safety**: Staging URLs only included in development
2. **Consistency**: Same configuration across all apps
3. **Maintainability**: Single source of truth for image domains
4. **Flexibility**: Easy to customize per app needs
5. **Type Safety**: Full TypeScript support
