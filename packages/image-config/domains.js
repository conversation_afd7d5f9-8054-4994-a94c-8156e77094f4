/**
 * Get image domains configuration based on environment
 * @returns {Array} Array of domain strings for Next.js images.domains
 */
function getImageDomains() {
  const baseDomains = [
    'axnqxdrfqrex.compat.objectstorage.me-jeddah-1.oraclecloud.com',
    'rentoor-bucket.s3.eu-central-1.amazonaws.com',
    'localhost',
  ];

  // Add staging domain only in development
  if (process.env.NODE_ENV === 'development') {
    baseDomains.push('stg-v3.rentoor.com');
  }

  return baseDomains;
}

/**
 * Get remote patterns configuration based on environment and app type
 * @param {Object} options - Configuration options
 * @param {boolean} options.includeGoogleMaps - Whether to include Google Maps domains
 * @param {boolean} options.includePexels - Whether to include Pexels domains
 * @param {Array} options.additionalPatterns - Additional custom patterns
 * @returns {Array} Array of remote pattern objects for Next.js images.remotePatterns
 */
function getImageRemotePatterns(options = {}) {
  const {
    includeGoogleMaps = false,
    includePexels = false,
    additionalPatterns = [],
  } = options;

  const basePatterns = [
    {
      protocol: 'https',
      hostname: 'axnqxdrfqrex.compat.objectstorage.me-jeddah-1.oraclecloud.com',
    },
    {
      protocol: 'https',
      hostname: 'rentoor-bucket.s3.eu-central-1.amazonaws.com',
    },
    {
      protocol: 'http',
      hostname: 'localhost',
    },
  ];

  // Add staging pattern only in development
  if (process.env.NODE_ENV === 'development') {
    basePatterns.push({
      protocol: 'https',
      hostname: 'stg-v3.rentoor.com',
    });
  }

  // Add Google Maps patterns if requested
  if (includeGoogleMaps) {
    basePatterns.push({
      protocol: 'https',
      hostname: 'maps.googleapis.com',
    });
  }

  // Add Pexels patterns if requested
  if (includePexels) {
    basePatterns.push({
      protocol: 'https',
      hostname: 'images.pexels.com',
    });
  }

  // Add any additional custom patterns
  basePatterns.push(...additionalPatterns);

  return basePatterns;
}

/**
 * Get complete image configuration for Next.js
 * @param {Object} options - Configuration options
 * @param {boolean} options.includeGoogleMaps - Whether to include Google Maps domains
 * @param {boolean} options.includePexels - Whether to include Pexels domains
 * @param {Array} options.additionalPatterns - Additional custom patterns
 * @param {boolean} options.unoptimized - Whether to disable image optimization
 * @returns {Object} Complete Next.js images configuration
 */
function getImageConfig(options = {}) {
  const { unoptimized = true, ...patternOptions } = options;

  return {
    unoptimized,
    domains: getImageDomains(),
    remotePatterns: getImageRemotePatterns(patternOptions),
  };
}

module.exports = {
  getImageDomains,
  getImageRemotePatterns,
  getImageConfig,
};
