#!/usr/bin/env node

/**
 * Test script to verify image configuration works correctly
 */

const { presets, createImageConfig, getImageDomains, getImageRemotePatterns } = require('./index.js');

console.log('🧪 Testing @rnt/image-config package...\n');

// Test environment detection
console.log('📍 Environment:', process.env.NODE_ENV || 'development');

// Test basic domain configuration
console.log('\n🌐 Testing getImageDomains():');
const domains = getImageDomains();
console.log('Domains:', domains);

// Test remote patterns
console.log('\n🔗 Testing getImageRemotePatterns():');
const patterns = getImageRemotePatterns({ includeGoogleMaps: true, includePexels: true });
console.log('Remote patterns:', JSON.stringify(patterns, null, 2));

// Test main app preset
console.log('\n🏠 Testing main app preset:');
const mainConfig = presets.main();
console.log('Main config:', JSON.stringify(mainConfig, null, 2));

// Test gust app preset
console.log('\n🌟 Testing gust app preset:');
const gustConfig = presets.gust();
console.log('Gust config:', JSON.stringify(gustConfig, null, 2));

// Test custom configuration
console.log('\n⚙️ Testing custom configuration:');
const customConfig = createImageConfig({
  includeGoogleMaps: true,
  includePexels: false,
  enableOptimization: true,
  additionalPatterns: [
    {
      protocol: 'https',
      hostname: 'example.com',
    },
  ],
});
console.log('Custom config:', JSON.stringify(customConfig, null, 2));

// Verify staging URL is only included in development
console.log('\n🔍 Verification checks:');
const hasStaging = domains.includes('stg-v3.rentoor.com');
const shouldHaveStaging = process.env.NODE_ENV !== 'production';
console.log(`✅ Staging URL included: ${hasStaging} (expected: ${shouldHaveStaging})`);

// Verify gust has Google Maps
const gustHasGoogleMaps = gustConfig.remotePatterns.some(p => p.hostname === 'maps.googleapis.com');
console.log(`✅ Gust has Google Maps: ${gustHasGoogleMaps} (expected: true)`);

// Verify main doesn't have Google Maps
const mainHasGoogleMaps = mainConfig.remotePatterns.some(p => p.hostname === 'maps.googleapis.com');
console.log(`✅ Main has Google Maps: ${mainHasGoogleMaps} (expected: false)`);

console.log('\n🎉 All tests completed!');
