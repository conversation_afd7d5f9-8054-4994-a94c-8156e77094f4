const {
  getImageDomains,
  getImageRemotePatterns,
  getImageConfig,
} = require('./domains');

const {
  getImageOptimization,
  getImageLoader,
  getCompleteImageOptimization,
} = require('./optimization');

/**
 * Create a complete Next.js image configuration
 * @param {Object} options - Configuration options
 * @param {boolean} options.includeGoogleMaps - Include Google Maps domains
 * @param {boolean} options.includePexels - Include Pexels domains
 * @param {Array} options.additionalPatterns - Additional remote patterns
 * @param {boolean} options.enableOptimization - Enable image optimization
 * @param {string} options.loaderType - Image loader type
 * @param {string} options.customLoaderPath - Custom loader path
 * @returns {Object} Complete Next.js images configuration
 */
function createImageConfig(options = {}) {
  const {
    includeGoogleMaps = false,
    includePexels = false,
    additionalPatterns = [],
    enableOptimization = process.env.NODE_ENV === 'production',
    loaderType = 'default',
    customLoaderPath = null,
    ...optimizationOptions
  } = options;

  // Get domain configuration
  const domainConfig = getImageConfig({
    includeGoogleMaps,
    includePexels,
    additionalPatterns,
    unoptimized: !enableOptimization,
  });

  // Get optimization configuration
  const optimizationConfig = getCompleteImageOptimization({
    enableOptimization,
    loaderType,
    customLoaderPath,
    ...optimizationOptions,
  });

  // Merge configurations
  return {
    ...domainConfig,
    ...optimizationConfig,
  };
}

/**
 * Preset configurations for different apps
 */
const presets = {
  /**
   * Main app configuration
   */
  main: () => createImageConfig({
    includeGoogleMaps: false,
    includePexels: false,
    enableOptimization: false, // Currently disabled as per existing config
  }),

  /**
   * Gust app configuration
   */
  gust: () => createImageConfig({
    includeGoogleMaps: true,
    includePexels: true,
    enableOptimization: false, // Currently disabled as per existing config
  }),

  /**
   * Production optimized configuration
   */
  production: (appOptions = {}) => createImageConfig({
    enableOptimization: true,
    minimumCacheTTL: 3600, // 1 hour
    formats: ['image/webp', 'image/avif'],
    ...appOptions,
  }),

  /**
   * Development configuration
   */
  development: (appOptions = {}) => createImageConfig({
    enableOptimization: false,
    ...appOptions,
  }),
};

module.exports = {
  // Main functions
  createImageConfig,
  presets,
  
  // Domain functions
  getImageDomains,
  getImageRemotePatterns,
  getImageConfig,
  
  // Optimization functions
  getImageOptimization,
  getImageLoader,
  getCompleteImageOptimization,
};
