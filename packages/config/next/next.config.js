/** @type {import('next').NextConfig} */
const { presets } = require('@rnt/image-config');

const nextConfig = {
  compiler: {
    removeConsole: {
      exclude: ['error', 'log', 'warning'],
    },
  },
  reactStrictMode: true,
  output: 'standalone',
  env: {
    NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV || 'development',
    NEXT_PUBLIC_BACKEND_URL: process.env.NEXT_PUBLIC_BACKEND_URL,
    NEXT_PUBLIC_GOOGLE_MAP_API_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY,
    NEXT_PUBLIC_GEO_IP_ADDRESS: process.env.NEXT_PUBLIC_GEO_IP_ADDRESS || 'https://ipapi.co/json',
    NEXT_PUBLIC_PHONE_RETRY_TIME: process.env.NEXT_PUBLIC_PHONE_RETRY_TIME || 120,
    NEXT_PUBLIC_HOST_URL: process.env.NEXT_PUBLIC_HOST_URL,
    NEXT_PUBLIC_ABLY_API_KEY: process.env.NEXT_PUBLIC_ABLY_API_KEY,
    NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    NEXT_PUBLIC_GOOGLE_MAP_ID: process.env.NEXT_PUBLIC_GOOGLE_MAP_ID,
  },
  // Default image configuration - apps can override this
  images: presets.main(),
  webpack(config) {
    config.cache = {
      type: 'filesystem',
      compression: 'gzip', // افتراضيًا true
      buildDependencies: {
        config: [__filename],
      },
    };
    if (process.env.NEXT_WEBPACK_USEPOLLING) {
      config.watchOptions = {
        ignored: '**/node_modules',
        poll: 6000,
        aggregateTimeout: 3000,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
