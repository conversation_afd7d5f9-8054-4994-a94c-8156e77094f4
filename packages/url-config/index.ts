const getEnv = (): 'development' | 'production' => {
  if (process.env.NODE_ENV === 'production') {
    return 'production';
  }

  return 'development';
};

const environmentConfig = {
  development: {
    api: 'https://stg-v3.rentoor.com/api',
    host: 'https://stg-web.rentoor.com',
    images: ['stg-v3.rentoor.com'],
  },
  production: {
    api: 'https://stg-v3.rentoor.com/api',
    host: 'https://stg-web.rentoor.com',
    images: ['stg-v3.rentoor.com'],
  },
};

const ENV = getEnv();

export const API_URL = environmentConfig[ENV].api;
export const HOST_URL = environmentConfig[ENV].host;
export const IMAGE_DOMAINS = environmentConfig[ENV].images;