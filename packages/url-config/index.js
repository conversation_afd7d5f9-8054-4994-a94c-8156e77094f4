const getEnv = () => {
  if (process.env.NODE_ENV === 'production') {
    return 'production';
  }

  return 'development';
};

const environmentConfig = {
  development: {
    api: 'https://stg-v3.rentoor.com/api',
    host: 'https://stg-web.rentoor.com',
    images: [
      'axnqxdrfqrex.compat.objectstorage.me-jeddah-1.oraclecloud.com',
      'rentoor-bucket.s3.eu-central-1.amazonaws.com',
      'localhost',
      'stg-v3.rentoor.com',
    ],
  },
  production: {
    api: 'https://stg-v3.rentoor.com/api',
    host: 'https://stg-web.rentoor.com',
    images: [
      'axnqxdrfqrex.compat.objectstorage.me-jeddah-1.oraclecloud.com',
      'rentoor-bucket.s3.eu-central-1.amazonaws.com',
      'localhost',
    ],
  },
};

const ENV = getEnv();

const API_URL = environmentConfig[ENV].api;
const HOST_URL = environmentConfig[ENV].host;
const IMAGE_DOMAINS = environmentConfig[ENV].images;

module.exports = {
  API_URL,
  HOST_URL,
  IMAGE_DOMAINS,
  getEnv,
  environmentConfig,
};
