import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import Locale from 'intl-locale-textinfo-polyfill';
import axios from 'axios';
import { parse } from 'date-fns/parse';
import { format } from 'date-fns/format';
import { ar, enUS } from 'date-fns/locale';
import { type Direction, type ErrorResponse } from '@/types/common';
import type { Locale as LC } from '@/lib/i18n/i18n-config';
import { DATE_FORMAT_GENERIC } from '@/constants';
import { type SpecificPrice } from '@/types/room-details';
import { type NightPrice } from '@/types/homepage';
import { addDays, differenceInDays, getDay } from 'date-fns';
import { getItiInputElement } from '@/components/hostPages/setting-property/manage-property/settings/NotificationNumber/OtherNumber/helpers';

export const cn = (...inputs: ClassValue[]): string => {
  return twMerge(clsx(inputs));
};

export const getLayoutDirection = (locale: LC): Direction => {
  const { direction } = new Locale(locale).textInfo;

  return direction as Direction;
};

export const getCurrentLocale = () => {
  if (typeof document === 'undefined') return 'ar'; // Default to 'ar' on server-side
  const cookies = document.cookie.split('; ');
  const localeCookie = cookies.find((cookie) => cookie.startsWith('Next-Locale='));
  const locale = localeCookie ? localeCookie.split('=')[1] : 'ar'; // Default to 'ar' if no cookie found
  return locale;
};

export const getElementOffsetY = (elementId: string): number => {
  if (typeof document === 'undefined') return 0; // Default to 0 on server-side
  const el = document.getElementById(elementId);
  return el?.offsetTop ?? 0;
};

export const getValidationErrors = (errors: ErrorResponse['response']['data']['errors']): [string, string[]][] => {
  return Object.entries(errors);
};

const isValidationResponse = (response: ErrorResponse['response']): response is ErrorResponse['response'] => {
  return (
    response.status === 422 && typeof response.data.message === 'string' && typeof response.data.errors === 'object'
  );
};

const isForbiddenResponse = (response: ErrorResponse['response']): response is ErrorResponse['response'] => {
  return (
    response.status === 403 && typeof response.data.message === 'string' && typeof response.data.errors === 'object'
  );
};

const isValidationError = (error: unknown): error is ErrorResponse => {
  return Boolean(axios.isAxiosError(error) && error.response && isValidationResponse(error.response));
};

const isForbiddenError = (error: unknown): error is ErrorResponse => {
  return Boolean(axios.isAxiosError(error) && error.response && isForbiddenResponse(error.response));
};

export const getMessagesFromResponse = (e: unknown): [string, string[]][] | string => {
  if (isValidationError(e)) {
    const errors = getValidationErrors(e.response.data.errors);
    if (errors.length > 0) return getValidationErrors(e.response.data.errors);
    return e.response.data.message;
  }
  if (isForbiddenError(e)) return e.response.data.message;
  return [];
};

export const getDomainParam = (): string => {
  const urlParams = new URLSearchParams(window.location.search);

  return urlParams.get('tenant') ?? '';
};

export const parseDateToString = (dt: Date): string => {
  if (!dt || !(dt instanceof Date) || isNaN(dt.getTime())) {
    console.error('Invalid date provided to parseDateToString:', dt);
    return '';
  }
  return format(dt, DATE_FORMAT_GENERIC);
};

export const parseDateToLocalizedDate = (
  date: Date | string,
  lang: 'ar' | 'en',
  options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
  }
): string => {
  if (date) {
    const formatter = new Intl.DateTimeFormat(lang, options);
    const finalDate = new Date(date);
    const formattedDate = formatter.format(finalDate);
    return formattedDate;
  } else {
    return '';
  }
};

export const parseStringToDate = (dt: string): Date => parse(dt, DATE_FORMAT_GENERIC, new Date());

export const getNewHeightFromDivWidth = (width: number, height: number, divWidth: number): number => {
  const aspect = width / height;
  return Math.ceil(divWidth / aspect);
};

export const getPriceDetails = (price: SpecificPrice | NightPrice | undefined): [number, number, boolean, number] => {
  let total = 0;
  let perNight = 0;
  let discount = 0;
  let hasDiscount = false;
  if (price) {
    if ('avg_night_price' in price && 'avg_night_price_discount' in price) {
      perNight = formattedNumberWithDecimal(price.avg_night_price);
      discount = formattedNumberWithDecimal(price.avg_night_price_discount);
      hasDiscount = perNight !== discount;
    } else if ('per_night_price' in price) {
      perNight = formattedNumberWithDecimal(price.per_night_price);
    }
    total = formattedNumberWithDecimal(price.total_price);
  }

  return [perNight, discount, hasDiscount, total];
};

export const formatDateRange = (checkinDate: string, checkoutDate: string): string => {
  const lang = getCurrentLocale();
  const checkin = new Date(checkinDate || new Date());
  const checkout = new Date(checkoutDate || new Date());

  const checkinDay = format(checkin, 'd', { locale: lang === 'ar' ? ar : enUS }); // Day of check-in
  const checkoutDay = format(checkout, 'd', { locale: lang === 'ar' ? ar : enUS }); // Day of check-out
  const month = format(checkin, 'MMMM', { locale: lang === 'ar' ? ar : enUS }); // Month name in Arabic
  const monthCheckout = format(checkout, 'MMMM', { locale: lang === 'ar' ? ar : enUS }); // Month name in Arabic

  return `${checkinDay} ${month} - ${checkoutDay} ${monthCheckout}`;
};

export const convertToTime = (dateTimeString: string) => {
  const [date, time] = dateTimeString.split(' ');
  const [hour, minute] = time.split('-');
  return `${hour}:${minute}`;
};

// Function to format input time and date
export const formatDateTime = (date: string, time: string) => {
  const lang = getCurrentLocale();

  // Combine the date and time into a single Date object
  const dateTimeString = `${date}T${time}`;
  const dateTime = new Date(dateTimeString);

  // Check if dateTime is valid
  if (isNaN(dateTime.getTime())) {
    console.error('Invalid date/time provided:', dateTimeString);
    return 'Invalid date/time';
  }

  // Format the time as '8:00 صباحًا'
  const timeFormatted = format(dateTime || new Date(), 'h:mm a', { locale: lang === 'ar' ? ar : enUS });

  // Format the date as '28/11/2022'
  const dateFormatted = format(dateTime || new Date(), 'dd/MM/yyyy', { locale: lang === 'ar' ? ar : enUS });

  // Combine both to get the final result '8:00 صباحًا - 28/11/2022'
  return ` ${dateFormatted} - ${timeFormatted} `;
};
export function startTimer(targetDateTime: string | Date, onUpdate: (timeRemaining: string, isPast: boolean) => void) {
  // Convert the target date to a timestamp
  const targetTime = new Date(targetDateTime).getTime();

  // Function to calculate the time difference
  const calculateTimeDifference = () => {
    const currentTime = new Date().getTime();
    const timeDifference = targetTime - currentTime;

    // Check if the target time has passed
    const isPast = timeDifference < 0;

    // Calculate time components
    const absDifference = Math.abs(timeDifference);
    // const days = Math.floor(absDifference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((absDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((absDifference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((absDifference % (1000 * 60)) / 1000);

    // Format the result as day hr:min, showing day if greater than 0
    let timeRemaining = '';

    timeRemaining += `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    // Return both time remaining and whether the time is in the past
    return { timeRemaining, isPast };
  };

  // Start the timer and update the callback every second
  const intervalId = setInterval(() => {
    const { timeRemaining, isPast } = calculateTimeDifference();

    // Call the callback function to update the UI or any other logic
    onUpdate(timeRemaining, isPast);

    // If the time has passed, clear the interval
    if (isPast) {
      clearInterval(intervalId);
    }
  }, 1000); // Update every second

  // Return a function to stop the timer manually if needed
  return () => clearInterval(intervalId);
}

// Custom function to get the Arabic day of the week and month
export const formatReservationDates = (
  checkinDate: Date | string | undefined,
  checkoutDate: Date | string | undefined,
  locale: string,
  new_number_of_nights?: string
): string => {
  const checkin = new Date(checkinDate || new Date());
  const checkout = new Date(checkoutDate || new Date());

  // Validate dates before using them
  if (isNaN(checkin.getTime()) || isNaN(checkout.getTime())) {
    console.error('Invalid dates provided to formatReservationDates:', { checkinDate, checkoutDate });
    return '';
  }

  const numberOfNights = new_number_of_nights || differenceInDays(checkout, checkin);

  // Format the checkin and checkout dates in Arabic with day of the week and month name
  const formattedCheckin = format(checkin, 'EEEE d MMMM', { locale: ar });
  const formattedCheckout = format(checkout, 'd MMMM', { locale: ar });

  // Determine the correct word for nights
  let nightText = '';
  if (locale === 'ar') {
    nightText = `(${numberOfNights} ليلة/ليالي)`;
  } else {
    nightText = `(${numberOfNights} night/nights)`;
  }

  return `${formattedCheckin} - ${formattedCheckout} ${nightText}`;
};

export const calculateTotalPrice = (
  checkinDate: Date | string | undefined,
  checkoutDate: Date | string | undefined,
  pre_night: number | undefined
): number | string => {
  if (pre_night === undefined) {
    return 'Price per night is not available';
  }

  const checkin = new Date(checkinDate || new Date());
  const checkout = new Date(checkoutDate || new Date());

  // Calculate the number of nights
  const numberOfNights = differenceInDays(checkout, checkin);

  // Calculate the total price
  const totalPrice = pre_night * numberOfNights;

  return totalPrice;
};

export const calculatePriceDifference = (oldPrice: number, newPrice: number) => {
  // Calculate the price difference
  const priceDifference = newPrice - oldPrice;
  return priceDifference;
};

export const formatTimeTo12Hr = (time: string | undefined, isArabic: string): string | null => {
  if (!time) return null;
  const hours = parseInt(time.split(':')[0]);
  const minutes = time.split(':')[1];

  const isPM = hours >= 12;
  const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
  const ampm = isArabic === 'ar' ? (isPM ? 'م' : 'ص') : isPM ? 'PM' : 'AM';

  return `${formattedHours}:${minutes} ${ampm}`;
};

export const getNextFriday = (date = new Date()) => {
  const currentDay = getDay(date);
  const daysUntilFriday = (5 - currentDay + 7) % 7 || 7;
  return addDays(date, daysUntilFriday);
};

export const formattedNumberWithDecimal = (number: number = 0, decimals: number = 2) =>
  number % 1 === 0 ? number : parseFloat(number.toFixed(decimals));

export const getFileNameFromUrl = (url: string) => {
  try {
    return new URL(url).pathname.split('/').pop();
  } catch (error) {
    return url; // Return the original value if it's not a valid URL
  }
};

const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.setAttribute('crossOrigin', 'anonymous'); // to avoid CORS issues
    image.src = url;
  });

export async function getCroppedImg(
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number },
  rotation = 0
): Promise<Blob> {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  const safeArea = Math.max(image.width, image.height) * 2;

  canvas.width = safeArea;
  canvas.height = safeArea;

  if (!ctx) throw new Error('Canvas context not found');

  ctx.translate(safeArea / 2, safeArea / 2);
  ctx.rotate((rotation * Math.PI) / 180);
  ctx.translate(-safeArea / 2, -safeArea / 2);
  ctx.drawImage(image, (safeArea - image.width) / 2, (safeArea - image.height) / 2);

  const data = ctx.getImageData(
    (safeArea - image.width) / 2 + pixelCrop.x,
    (safeArea - image.height) / 2 + pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height
  );

  canvas.width = pixelCrop.width;
  canvas.height = pixelCrop.height;
  ctx.putImageData(data, 0, 0);

  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      }
    }, 'image/png');
  });
}

export const dateToString = (date?: Date | string) => {
  const defaultDateFormat = 'yyyy-MM-dd';

  if (!date) return '';
  if (typeof date === 'string') return date;
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    console.error('Invalid date provided to dateToString:', date);
    return '';
  }
  return format(date, defaultDateFormat);
};

export const convertImageUrlToFile = async (imageUrl: string): Promise<File> => {
  const response = await fetch(imageUrl);
  const data = await response.blob();
  const file = new File([data], 'image.jpg', { type: 'image/jpeg' });
  return file;
};

export const convertImageUrlsToFileList = async (imageUrls: string[]): Promise<FileList> => {
  const fileList = new DataTransfer();
  await Promise.all(
    imageUrls.map(async (imageUrl) => {
      const file = await convertImageUrlToFile(imageUrl);
      fileList.items.add(file);
    })
  );

  return fileList.files;
};

export const getPhoneDetails = (fullNumber: string) => {
  const phone_code = getItiInputElement?.()?.getSelectedCountryData?.()?.dialCode;
  if (!phone_code) return { phone_number: '', phone_code: '' };

  const phone_number = fullNumber?.substring?.(phone_code.length + 1);
  return { phone_number, phone_code };
};
