import HostMobileAlert from '@/components/hostPages/common/HostMobileAlert';
import HostLayout from '@/components/layouts/host-layout';
import { Toaster } from '@/components/ui/sonner';
import { getScopedI18n } from '@/lib/i18n/server-translator';

type LayoutProps = {
  children: React.ReactNode;
};

export async function generateMetadata() {
  const t = await getScopedI18n('seo.host');
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
  };
}

const HostLayoutCOntainer: React.FC<LayoutProps> = ({ children }) => {
  return (
    <HostLayout>
      <div className="bg-white-full z-50 px-6 pb-4 pt-8 md:hidden">
        <HostMobileAlert />
      </div>

      {children}
      <Toaster />
    </HostLayout>
  );
};

export default HostLayoutCOntainer;
