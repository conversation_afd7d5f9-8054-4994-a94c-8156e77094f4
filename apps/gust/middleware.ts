// import { ROUTES } from './constants';
import { PROTECTED_ROUTES } from './constants';
import { getAccessToken, getAccount, getLang, getProfile } from './queries/authentication/access-token';
import { UserAccount } from './types/profile';

const { createI18nMiddleware } = require('next-international/middleware');
const { i18n } = require('@/lib/i18n/i18n-config');
const { NextResponse } = require('next/server');

const I18nMiddleware = createI18nMiddleware(i18n);
const TOKEN_KEY = 'access_token';

// 1. Define protected routes
const verifyRoutes = ['/host'];
const PROTECTED_HOST_ROUTES = ['/host/administrativeAccounts'];
const PROTECTED_MY_RENTOOR_HOST_ROUTES = ['/host/myRentoor'];
const notVerifyRoutes = ['/verify'];

export const middleware = async (request) => {
  const url = request.nextUrl; // Use request.nextUrl instead of request.url
  const path = url.pathname;

  // Skip middleware for Sentry monitoring routes
  if (path.startsWith('/monitoring')) {
    return NextResponse.next();
  }

  // user data
  const tokeParam = url.searchParams.get('token');

  const token = (await getAccessToken()) ?? tokeParam;

  const userAccountCookie = await getAccount();
  const userProfileCookie = await getProfile();
  const response = NextResponse.next();

  // console.log({ tokeParam })

  // 4. Check if the token exists in the query parameters
  if (tokeParam) {
    response.cookies.set(TOKEN_KEY, tokeParam);
    url.searchParams.delete('token');
    return NextResponse.redirect(url.toString(), { headers: response.headers });
  }

  // Extract language from the path (e.g., 'ar' in 'ar/account')
  const [, lang] = path.split('/');

  if (!lang) {
    const defaultLang = (await getLang()) || 'ar';
    const redirectUrl = new URL(`/${defaultLang}`, url.origin);
    return NextResponse.redirect(redirectUrl.toString(), { headers: response.headers });
  }

  // 2. Check if the path matches any of the protected routes
  const isProtectedRoute = PROTECTED_ROUTES.some((route) => path.startsWith(`/${lang}${route}`));
  const isVerifyRoute = verifyRoutes.some((route) => path.startsWith(`/${lang}${route}`));
  const isProduct_host_Route = PROTECTED_HOST_ROUTES.some((route) => path.startsWith(`/${lang}${route}`));
  const isProduct_my_rentoor_host_Route = PROTECTED_MY_RENTOOR_HOST_ROUTES.some((route) =>
    path.startsWith(`/${lang}${route}`)
  );
  const isNotVerifyRoutes = notVerifyRoutes.some((route) => path.startsWith(`/${lang}${route}`));


  // Parse user profile cookie
  let userAccount: UserAccount | null = null;
  let userProfile: any = null;

  if (userAccountCookie && userProfileCookie) {
    try {
      userAccount = JSON.parse(userAccountCookie) as UserAccount;
      userProfile = JSON.parse(userProfileCookie) as any;
    } catch (error) {
      console.error('Failed to parse userProfile cookie:', error);
    }
  }

  const user_account_verification_status = userAccount?.status;
  const host_commercial_registration_status = userAccount?.host_commercial_registration?.status;

  const role_type = userProfile?.role_type?.role_type;
  const Account_type = userAccount?.host_commercial_registration?.account_type;

  const allowed_host =
    user_account_verification_status === 'Verified' && host_commercial_registration_status === 'Verified';

  const shouldBlockVerifyPage =
    isNotVerifyRoutes &&
    (
      (user_account_verification_status === 'Verified' && host_commercial_registration_status === 'Verified') ||
      (user_account_verification_status === 'Pending' && host_commercial_registration_status === 'Verified')
    );

  const isVerifyRouteAndHost = isProduct_host_Route && role_type === 'Property Manager';
  const isMyRentoorVerifyRouteAndHost = isProduct_my_rentoor_host_Route && Account_type === 'Individual';

  if (isProtectedRoute && !token) {
    const redirectUrl = new URL(`/${lang}`, url.origin); // Use url.origin for the base URL
    // redirectUrl.searchParams.set('page', '1'); // Add '?page=1' query
    return NextResponse.redirect(redirectUrl, { headers: response.headers });
  }
  // 4. Check if the user is a host and the route is not allowed for hosts
  if (isVerifyRouteAndHost) {
    const redirectUrl = new URL(`/${lang}/host`, url.origin); // Use url.origin for the base URL
    return NextResponse.redirect(redirectUrl, { headers: response.headers });
  }

  if (isMyRentoorVerifyRouteAndHost) {
    const redirectUrl = new URL(`/${lang}/host`, url.origin); // Use url.origin for the base URL
    return NextResponse.redirect(redirectUrl, { headers: response.headers });
  }

  if (isVerifyRoute && !allowed_host) {
    const redirectUrl = new URL(`/${lang}`, url.origin); // Use url.origin for the base URL
    // redirectUrl.searchParams.set('page', '1'); // Add '?page=1' query

    return NextResponse.redirect(redirectUrl, { headers: response.headers }); // Redirect to the home page
  }

  if (shouldBlockVerifyPage) {
    const redirectUrl = new URL(`/${lang}`, url.origin);
    // redirectUrl.searchParams.set('forceReload', '1');
    return NextResponse.redirect(redirectUrl, { headers: response.headers });
  }




  // 5. Apply I18n middleware for other routes
  return I18nMiddleware(request);
};

export const config = {
  matcher: ['/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt|monitoring).*)'],
};