export type AvailableCountries = Country[];

type Country = {
  id: number;
  long_name: string;
  short_name: string;
  phone_code: number;
};

export type AuthenticationResponse = Phone & {
  status: string;
  is_exists: boolean;
};

export type Phone = {
  phone_code: number | undefined;
  phone_number: number | undefined;
};
export type LoginSteps = 'login' | 'verify' | 'register' | 'done' | 'new-host-account';

export type User = {
  access_token: string;
  dob: string;
  email_id: string;
  first_name: string;
  full_name: string;
  id: number;
  profile_picture: string;
  is_exists: boolean;
  is_host: boolean;
  last_name: string;
  gender: string;
  phone_number_full: string;
  role_type: { id: null; role_type: string, is_manager: boolean, status: string };
  email_time: string;
  phone_time: string;
  rejected_reason?: string;
} & AuthenticationResponse;
