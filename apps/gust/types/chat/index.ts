export interface ChatMessageItem {
  id: number;
  main_id: number;
  room_classification_id: number;
  user_name: string;
  name: string;
  sub_name: any;
  message: string | string[];
  reservation_message: any;
  message_title: string;
  message_type: string;
  send_from: string;
  content_type: string;
  created_at: string;
}

export interface GuestMessagesParams {
  status?: string;
  start_date?: string;
  end_date?: string;
  order_by?: string;
  room_id?: string;
  page?: string | number;
  enabled?: boolean;
}

export interface ChatRoomItem {
  id: number;
  reservation_id: number;
  name: string;
  sub_name: string;
  units: Unit[];
  status: string;
  message: string;
  total_price: number;
  checkin_date: string;
  checkout_date: string;
  original_image: string;
  number_of_guest: number;
  created_at: string;
  new_chat_id?: string | undefined;
  room_classification_id?: any;
}

export interface Unit {
  id: number;
  unit_number: string;
}
