// import { IConfig } from 'next-sitemap';
import { HOST_URL } from '../../packages/url-config';

const config = {
  siteUrl: process.env.SITE_URL || `${HOST_URL}`, // Replace with your website's URL
  generateRobotsTxt: true, // Generate robots.txt file
  generateIndexSitemap: false,
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
  },
  exclude: ['/admin/*'], // Exclude specific URLs
  additionalPaths: async (config) => [
    await config.transform(config, '/'),
    await config.transform(config, '/search'),
    await config.transform(config, '/about'),
    await config.transform(config, '/becomeHost'),
    await config.transform(config, '/protectionHost'),
    await config.transform(config, '/contact'),
    await config.transform(config, '/terms-conditions'),
    await config.transform(config, '/policy'),
    await config.transform(config, '/term-use'),
  ],
  sitemapSize: 5000, // Max number of URLs per sitemap file
  changefreq: 'daily', // Frequency for content updates
  priority: 0.7, // Default priority for pages
};

module.exports = config;
