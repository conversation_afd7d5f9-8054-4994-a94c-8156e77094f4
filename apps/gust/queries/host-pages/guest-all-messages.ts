import { useInfiniteQuery } from '@tanstack/react-query';
import { useCurrentLocale } from '@/lib/i18n/client-translator';
import { API } from '@/lib/axios/axios';
import { keys } from '@/lib/react-query/keys';

export const useGetAllGuestMessages = ({ message_id = '', enabled = true }) => {
  const locale = useCurrentLocale();

  return useInfiniteQuery({
    queryKey: [...keys.getAllGuestMessages, locale, message_id],
    queryFn: ({ pageParam }) => API('', locale).get(`v2/messages?message_id=${message_id}&page=${pageParam}`),
    initialPageParam: 1,
    enabled,
    refetchOnWindowFocus: false,
    getNextPageParam: (lastPage: any) => {
      const { current_page, last_page } = lastPage?.data?.pages;
      return current_page < last_page ? current_page + 1 : undefined;
    },
  });
};
