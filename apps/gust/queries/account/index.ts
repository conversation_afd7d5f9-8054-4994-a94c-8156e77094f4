import { API } from "@/lib/axios/axios";
import { useCurrentLocale } from "@/lib/i18n/client-translator";
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

export const useEditProfile = (): UseMutationResult<AxiosResponse<any>> => {
  const locale = useCurrentLocale(); // Assuming locale is being used for API requests
  return useMutation({
    mutationFn: async (formData: FormData) => {
      return API('', locale).post(`/v2/profile`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data', // Set multipart headers
        },
      });
    },
  });
};

// delete account
export const useDeleteAccount = (): UseMutationResult<AxiosResponse<any>> => {

  return useMutation({
    mutationFn: async (reason: string) => {
      return API().delete(`/v2/profile/account`, { data: { reason } });
    },
  });
};