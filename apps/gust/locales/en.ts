import staticPage from './static-pages/en';
import common from './common/en';
import wallet from './wallet/en';
import chat from './hostPages/chat/en';
import notifications from './hostPages/notifications/en';
import {
  myPropertiesPage,
  addNewPropertyPage,
  settingPropertyPage,
  payoutsPage,
  myRentoorWebsite,
  verifyPage,
} from './hostPages/en';

export default {
  homepage: {
    search: {
      arrival_date: 'CheckIn',
      departure_date: 'CheckOut',
      add_date: 'Add dates',
      number_guests: 'Guests',
      guests: 'Guests',
      add_guests: 'Add Guests',
      search: 'Search',
      book: 'Book now',
      select_guest_count: 'Select guest count',
      required_search: 'Select Checkin and checkout dates to continue',
      confirm_booking: 'Confirm booking details',
      city: 'City',
      select_city: 'Select City',
      date: 'Date',
      search_city: 'search for city',
      theSearch: 'Search',
      propertyType: 'Property type',
      guest_count: 'Guests Count',
      search_by_property_name: 'Search by property name',
      search_by_property_name_placeholder: 'Example: <PERSON>',
      gust_search_description:
        'Determine the number of guests based on the number of children and the number of members who will be present at the property',
      check_in_and_delivery_date: 'Check-in and delivery date',
      selected_date_between: 'You have selected the period from {from} to {to}',
      oneNight: 'One Night',
      nights: '{count} Nights',
      send_booking_request: 'Send Booking Request',
      enter_date: 'Enter date',
      enter_arrival_date: 'Enter arrival date',
      enter_departure_date: 'Enter departure date',
      search_city_placeholder: 'Search for city',
      no_cities_found: 'No cities found.',
    },
    classification: {
      ad: 'Ad',
      night: 'night',
      total: 'Total',
      'show-all': 'show all',
      _: 'Classification',
    },
    reviews: {
      'client-opinions': 'What clients say about us',
    },
    location: {
      permessionError:
        'عفواً! ليس لدينا إذن بالوصول إلى موقعك. 🌍 لرؤية الأماكن القريبة، يرجى تمكين خدمات الموقع في إعداداتك.',
      link: 'Go to Location',
      location: 'Location in City',
      title:
        'The displayed location is the approximate location of the property, and the actual location of the property will be provided after the booking process.',
    },
    policy: {
      title: 'Reservation #',
      returns: 'Cancellation and returns policy',
      rules: 'Extra rules',
      payment_type: 'Payment type',
    },

    gallery: 'Gallery',
    amenities: {
      amenities: 'Amenities',
      no_amenities: 'None',
      room_number: 'Room {num}:',
      bathroom_number: 'Bathroom number {num}',
      kitchen_table_seats: 'Dinning table for {num}',
      bathroom_type_public: 'Shared public bathroom',
      bathroom_type_private: 'Private bathroom',
      private: 'private',
      public: 'public',
      indoor: 'indoor',
      outdoor: 'outdoor',
      bedroom: 'Bedroom',
      bathroom: 'Bathroom',
      kitchen: 'Kitchen',
      livingroom: 'Livingroom',
      livingroom_number: 'Living room number {num}',
      sofa_seats: 'Seats {num}',
      sofa_bed_seats: 'Sofa beds {num}',
      pools: 'Pools',
      pools_number: 'pool number {num}',
      pool_gradual: 'outdoor pool, no fence, gradual depth',
      pool_not_gradual: 'outdoor pool, no fence, no gradual depth',
      pool_depth: 'pool depth {depth} m',
      pool_depth_gradual: 'pool depth gradual from {from} m to {to} m',
      pool_dimensions: 'pool dimensions {width} m x {length} m',
      luxury: 'Luxuries',
    },
  },
  more_menu: {
    title: 'More',
    need_login_title: 'Enjoy all the features by logging in',
    need_login_description:
      'Sorry, you need to sign in to access account settings and more features. Sign in for the full experience.',
    language: 'Language',
    frequently_asked_questions: 'Frequently Asked Questions',
    contact_us: 'Contact Us',
    terms_of_use: 'Terms of Use',
    privacy_policy: 'Privacy Policy',
    choose_language: 'Choose language',
    account: 'Account',
    my_reservations: 'Reservations',
    about_us: 'About us',
    become_host_on_rentur: 'Become a Host on Rentoor',
    rentur_host_protection: 'Rentoor Host Protection',
    switch_to_host_mode: 'Switch to Host mode',
    logout: 'Logout',
  },

  inbox_page: {
    inbox: 'Inbox',
    chat,
    loginMessage:
      "Sorry, you are not logged in, so you can't access messages. Log in to communicate with hosts and receive updates",
  },
  my_reservations: {
    sortReservationsBy: 'Sort reservations by',
    exploreProperties: 'Explore Properties',
    searchByPropertyName: 'Search by Property name',
    reservations: 'Reservations',
    notFoundExpired:
      'Sorry, there are no Expired reservations in your account. Start exploring available properties now and book your perfect stay',
    notFoundCurrent:
      'Sorry, there are no current reservations in your account. Start exploring available properties now and book your perfect stay',
    loginMessage:
      "Sorry, you can only view reservations after you're logged in. Log in to start tracking your reservations and travel plans",
  },
  login_page: {
    enter_mobile_number_for_login: 'Enter mobile number to create an account or log in',
  },
  bottomMenu: {
    favorite: 'Favorite',
    more: 'More',
    search: 'Search',
    reservation: 'Reservation',
    inbox: 'Inbox',
  },
  property_search: {
    show_more: 'show more',
    show_less: 'show less',
    result_count: '{num} places available in the chosen location',
    price_range: 'Price range',
    search_city_placeholder: 'Search for city',
    no_cities_found: 'No cities found.',
    sort_by_property_name: 'Sort by property name',
    search_neighborhood_placeholder: 'Search for neighborhood',
    no_neighborhoods_found: 'No neighborhoods found.',
    filter: 'Filter results',
    mixed_filter: 'Mixed filter',
    filter_wishlist: 'Filter by your favorites list',
    youth: 'Families or Teens',
    teens: 'Teens',
    families: 'Families',
    teen_family: 'Teens & Families',
    guest_count: 'Filter by number of Guests',
    no_selection: 'None',
    bedrooms: 'Bedrooms',
    bedroom_amenities: 'Bedroom amenities',
    double_bed: 'Double beds',
    select_city: 'Select City',
    single_sofa_bed: 'Single beds and sofa beds',
    bathroom: 'Bathrooms',
    show_private_bathroom: 'Show private bathroom only',
    count: 'Count',
    bathroom_amenities: 'Restroom facilities',
    kitchen: 'Kitchens',
    show_private_kitchen: 'Show private kitchens only',
    table_for: 'Dining table for',
    kitchen_amenities: 'Kitchen facilities',
    seats: 'Councils and sessions',
    show_private_seats: 'Show private boards only',
    seats_amenities: 'Council and session facilities',
    pool: 'Swimming pool',
    pool_max_depth: 'Maximum pool depth in metres',
    pool_min_depth: 'Minimum pool depth in metres',
    pool_spec: 'Pool specifications',
    luxury_filter: 'Filter by amenities',
    unit_area: 'Unit area',
    search_property_name: 'Enter Name Property',
    meter: 'Meter',
    square_meter: 'm2',
    per_night: '/night',
    total: 'Total',
    unit_size: 'Unit area {size} m²',
    show_amenities: 'Show Amenities',
    no_result:
      'Sorry, there are no categories that currently match your selected filter. try Different search filters or browse our other properties to discover different offers.',
    most_rating: 'Most rated',
    default: 'default',
    min_price: 'Lowest price',
    max_price: 'Highest price',
    near_to_you: 'Nearest to you',
    sort_by: 'Sort by',
    offers: 'offers and sales',
    filterByProperty: 'Filter by Property',
    city: 'City',
    neighborhood: 'Neighborhood',
    select_neighborhood: 'Select Neighborhood',
    direction: 'Direction',
    sections: 'Sections',
    ratingFilter: 'Filter by Rating',
    andAbove: 'and Above',
    rating: 'Rating',
    filterByRating: 'Filter results by rating',
    filterByLocation: 'Filter results by Location',
    apply: 'Apply',
    resetAll: 'Reset All',
    availablePlaces: ' places available in the selected location',
    showMap: 'Show Map',
    showList: 'Show List',
    property: 'property',
    newest: 'newest',
    oldest: 'oldest',
    all: 'All',
    bookingDetails: 'Booking Details',
    bookingEdit: 'Booking Edit',
    propertyType: 'Property type',
    filterByPropertyType: 'Filter results by property type',
    filterByPrice: 'Filter by price',
    price: 'Price',
    hide_map: 'Hide Map',

    show_map: 'Show Map',
    place: 'Place',
    tourism_license: 'Ministry of Tourism License Number: {license}. ',
  },
  property_details: {
    detail: 'Details',
    location: 'Map',
    pictures: 'Pictures',
    reviews: 'Reviews',
    reservation_details: 'Reservation details',
    update_dates: 'You can update the previously selected dates to view the different prices',
    units: 'Units',
    unavailable: 'UNAVAILABLE',
    communicate_host: 'Communicate with host',
    got_question: 'Have questions?',
    rating: 'Rating',
    rating_based: 'Depending on {rating} rating',
    rating_based_google: 'Depending on {rating} rating from Google Maps',
    rating_based_both: 'Based on {total} rating ({rentoor} from Rentoor and {google} from google maps)',
    property_image: 'Property image',
    noReviewData: 'No review data',
    accuracy: 'Accuracy',
    service: 'Service',
    cleanliness: 'Cleanliness',
    locationReview: 'Location',
    entry: 'Entry',
    communication: 'Communication',
    load_more_months: 'Load more months',
    loading_more: 'Loading more',
  },
  reserve: {
    review_pay: 'Confirm and payment',
    back_details: 'Return to the Details Property',
    reservation_details: 'Reservation details',
    unauthorized_text: 'To continue booking, please log in.',
    unauthorized_desc: 'Signing in helps us secure your reservation and provide a better user experience.',
    price_details: 'Price details',
    night_price: '{nights} nights * {price} {curr}',
    total_price_with_tax_and_commission: 'Total price including tax and site commission {price} SAR',
    one_night_price: 'price (one night)',
    pay_details: 'Payment Details',
    subtotal: 'Subtotal',
    total_payments: 'Total Payments',
    service_fee: 'Service fees',
    tax_fee: 'Taxes',
    total_pay: 'The total amount',
    confirm_pay: 'Confirm and pay',
    payment_method: 'Payment method',
    credit_card: 'Credit card',
    apple_pay: 'Apple pay',
    selected_card: 'The selected card',
    add_card_desc: 'Start adding one of your cards to be able to complete the payment',
    add_card: 'Add a new card',
    use_wallet: 'Use wallet balance',
    use_coupon: 'Discount code',
    apply: 'Apply',
    wallet_balance_partial:
      'Payment from the wallet has been activated. The amount <span class="text-primary">{value} {currency}</span> will be deducted from the wallet, and the remainder of the reservation amount will be paid using one of the available payment methods.',
    wallet_balance_full:
      'Payment from the wallet has been activated. The entire amount <span class="text-primary">{value} {currency}</span> will be deducted from the wallet.',
    rules: 'Booking conditions',
    wallet: 'Wallet',
    transactions: 'Operation',
    add_date: 'Add date',
    expire_date: 'Expire date',
    value: 'Value',
    wallet_balance: 'Wallet balance',
    amount_checkin: 'Amount to pay upon entry',
    amount_pay_now: 'Amount to pay now',
    request_booking: 'Send booking request',
    payment_successful: 'Payment Successful',
    payment_successful_desc: 'Payment transaction was Successful',
    property_name: 'Property',
    navigate_booking_details: 'Booking details',
    nights: 'Nights',
    not_verified: 'Account not verified',
    verify:
      'Please complete the verification of your account to confirm your reservation. <a class="text-secondary" target="_blank" href="{link}">Click here</a> to verify',
    request_book_response:
      'A reservation request has been sent to {name}. The request will be responded to within 24 hours. You can communicate with the host via chat to speed up approval',
    pay: 'Pay',
    choose_payment_method: 'Choose how to pay',
    payment_description:
      'Choose whether you want to pay part of the amount now and the rest upon arrival, or pay the full amount upfront.',
    full_payment: 'Full Payment',
    full_payment_description: 'Pay the full amount now of {amount} SAR.',
    partial_payment_description:
      'You will pay {amount_to_pay_percent}% now of {partialAmount} SAR, and the remaining {remainingAmount_to_pay_percent}% upon arrival of {remainingAmount} SAR.',
    PartialPay: 'Partial Pay',
    FullPay: 'Full Pay',
    finished: 'Finished',
    allowed: 'Current',
    verificationMessage: 'Please complete your account verification to confirm the booking,',
    verificationLink: 'Click here',
    verificationSuffix: 'for verification.',
    accuracy: 'Accuracy',
    cleanliness: 'Cleanliness',
    checkin: 'Check-in',
    location: 'Location',
    communication: 'Communication',
    service: 'Service',
    describeExperience: 'Describe Your Experience',
    describeExperiencePlaceholder: 'Describe your experience with the host here',
    rateReservation: 'Rate Reservation',
    detailsReserve: 'Details of the reservation modification',
    modification_policy: 'Policy on modifications and cancellations',
    cancellation_details:
      'Cancellation is allowed, and the amount can be refunded two days before the check-in date. You can also modify/cancel some nights two days before the modified/canceled night.',
    reservation_date: 'Reservation Date',
    night_pre_price: 'Night price',
    flexible: 'Flexible',
    total_price: 'Total Price',
    extra_payment_required: 'Additional amount to pay',
    extra_payment_return: 'The amount that will be refunded',
    reservation_updated: 'The reservation details have been updated',
    rateGuestStay: 'Rate Guest Stay',
    ratingExplanation: "Rate the guest's stay from 1 to 5 stars, where 1 is unsatisfactory and 5 is excellent",
    describeGuestExperience: 'Please describe your experience with the guest',
    ratingImportance:
      'Your rating helps improve the quality of experiences between guests and hosts. Share your opinion about the guest and be part of the Rentor community that supports excellence and transparency',
    commentRequired: 'Please enter a comment',
    reserveError:
      'Payment failed. Please check your card details and try again. If the problem persists, please contact customer service for assistance',
  },
  shomoos: {
    searchDocumentType: 'Search for document type',
    shomoosModifiedTitle: 'Report to Shomoos (optional)',
    shomoosModifiedDesc:
      'This reservation data has been modified after it was previously submitted to the Shams system (such as changing dates or guest information). To ensure the accuracy of the data and its compliance with official authorities, please resubmit.',
    shomoosUploadedTitle: 'The data has been uploaded to Shomoos.',
    shomoosUploadedDesc:
      'The guest and escorts data has been successfully sent to the Shomoos security system of the Ministry of Interior. No further action is required – everything has been completed successfully.',
    editReservationDetails: 'Edit reservation data',
    dialogTitle: 'Send reservation data to Shomoos',
    confirmProcess: 'Confirm Process',
    mainGuestInfo: 'Main guest Information',
    mainGuestInfoDesc:
      'Please enter the main guest’s information accurately as it appears in the official documents, as it will be sent to the Shomoos system of the Ministry of Interior.',
    documentType: 'Document Type',
    selectDocumentType: 'Select document type',
    documentNo: 'Document Number',
    personalIdentity: 'Personal Identity',
    nationality: 'Nationality',
    dateOfBirth: 'Date of Birth',
    jordan: 'Jordan',
    nationalityIsRequired: 'Please select the Nationality',
    escorts: 'Escorts (if any)',
    escortsDesc:
      'If there’s more than one guest in this booking, you can add the escorts’ information here. All information will be submitted at once.',
    addNewEscort: 'Add New Escort',
    addNewEscortDetails: 'Add new escort details',
    actions: 'Actions',
    addTheEscort: 'Add the escort',
    confirmDeleteEscort: 'Confirm Escort Deletion',
    sendReservationDetails: 'Send Reservation details',
    confirmDeleteEscortDesc: 'Are you sure you want to delete the escort account with the following document number',
    sendShomoosReservationDetailsDesc:
      'As part of the main contract subscription with the Shomoos system, you can send the information for this registration directly through Rentoor',
    oneGuestAddEscortValidationMessage:
      'You cannot add an escort because the reservation contains only one guest (the main guest).',
    moreThanOneGuestAddEscortValidationMessage:
      'You cannot add more escorts. The number of guests in this reservation is {count}, and the number of people in Shomoos must match the number of guests in the reservation.',
    escortsCountLessThanGuestsMessage:
      'The number of people added to Shomoos is less than the number of guests in the reservation ({count}).',

    escortsCountMoreThanGuestsMessage:
      'The number of people added to Shomoos is more than the number of guests in the reservation ({count}).',

    reservationDataSentToShomoosSucessfully: 'Reservation data sent to shomoos successfully',
    showDetails: 'Show details',
    fullName: 'Full Name',
    reservationDataUpdatedSucessfully: 'Reservation data updated successfully',
    editEscort: 'Edit Escort',
    name: 'Name',
    ID: 'ID',
    theEscortDeletedSuccessfully: 'The Escort deleted successfully',
    tryAgain: 'Try again',
    unable_to_load_shomoos_data: 'Unable to load shomoos data',
    unable_to_load_shomoos_data_desc:
      'We encountered an issue while fetching shomoos data for this reservation. Please try again, and if the problem persists, please contact customer support.',
  },
  header: {
    login: 'Login',
    logout: 'Logout',
    contact: 'Contact us',
    book: 'Book now',
    ar: 'Arabic',
    en: 'English',
    search: {
      filter: 'Filter',
      sort: 'Sort',
    },
    profile: 'Profile',
    chats: 'Chats',
    notifications: 'Notifications',
    reservation: 'Reservation',
    favorite: 'Favorite',
    map: 'Map',
    hostNowBtn: {
      hostWithUs: 'Hosting',
      MoveToHos: 'Switch To Host',
      MoveToGuest: 'Switch To Guest',

      pendingMessage:
        'Thank you for your interest in joining Rentor as a host! We are currently reviewing your application to ensure all necessary requirements are met. You will be notified once the verification and approval process is complete.',
      rejectedMessage: 'It appears that your previous application was rejected due to. ',
      you_can_edit_message: 'You can edit your data and try again or contact support',

      okey: 'OK',
      edit_data: 'Edit Data',
      contact_with_Support: 'Contact Support',
      reject_application_host: 'Your application to join as a host has been rejected',
      awaitingApprovalMessage:
        'Your account has been successfully verified! All you need to do now is accept the invitation from the notifications page to join the host side and take advantage of all the features available to you',
    },
  },

  footer: {
    'privacy-policy': 'Privacy policy',
    'terms-of-service': 'Terms of service',
    'all-rights-reserved': 'Rentoor all rights reserved {date}©.',
    'site-powered-by': 'Site powered by Rentoor',
    reservation: 'Reservation conditions',
    reviews: 'Reviews',
    location: 'Location',
    gallery: 'Gallery',
    amenities: 'Amenities',
    classification: 'Classification',
    search: 'Search',
    about: 'About us',
    download: 'Download',
    'terms-of-use': 'Terms of Use',
    'copyright-policy': 'Copyright Policy',
    become_host: 'Become a Host on Rentoor',
    host_protection: 'Rentoor Host Protection',
    help_center: 'Help Center',
    cancellation_options: 'Cancellation Options',
    description:
      'Rentoor is your smart platform to explore, book, and manage vacation rentals across Saudi Arabia. From cozy chalets to luxury stays, we empower guests and hosts with seamless tools and trusted experiences.',
    company_commercial_label: 'Licensed by the Ministry of Tourism License No.:',
  },
  authentication: {
    login: 'Login',
    browse_as_Guest: 'Browse as a guest',
    create_account_now: 'Create Account',
    register: 'Register',
    searchPlaceholder: 'Search by country name, country code',
    register_subtitle:
      'Please enter the following data to create your account and make sure it is correct so that you can enjoy all the advantages of the application',
    register_complete: 'Account created successfully',
    register_complete_desc:
      'Congratulations, the account has been created successfully. You can now browse properties or verify the account to enjoy all our services',
    title: 'Enter your mobile phone number to log in or to create a new account.',
    register_title: 'There is no account linked to the entered mobile number',
    register_title_1: 'Please enter the following data in order for your account to be created',
    phone_number: 'Phone number',
    phone_code: 'Country code',
    send_code_desc:
      'We will send a message to the entered number containing a code to continue.\n' +
      'Make sure you entered your number correctly',
    verify_phone: 'Verify phone number',
    verify_phone_desc: 'Please enter the code to continue. We have sent the verification code by text message to ',
    verification_code: 'Verification code',
    no_code: "Haven't received a message yet?",
    resend_code: 'Send the code again within ',
    first_name: 'First name',
    last_name: 'Last name',
    gender: 'Gender',
    male: 'Male',
    female: 'Female',
    email: 'Email address',
    dob: 'Date of birth',
    verify_account: 'Verify account',
    explore_estate: 'Explore Estates',
    go_to_Home: 'Go To Home',
    logged_out: 'logged out successfully',
    logged_in: 'logged in successfully',
    resend_success: 'Verification code has been resent successfully',
    agreement_text: 'By continuing, you agree to our',
    terms_of_use: 'Terms of Use',
    privacy_policy: 'Privacy Policy',
    suffix: '.',
    edit_email: 'Edit Email',
    verify_email: 'Please enter the verification code sent to your current email:',
    verify_by_phone: 'Verify using mobile number',
    add_email_description: 'Please enter your new email address in the field below',
    follow_up: 'Follow_up',
    update_email_done: 'The account email has been successfully modified',
    back_to_profile: 'Back to account',
    edit_phone: 'Edit mobile number',
    update_phone_done: "The account's mobile number has been successfully modified",
    add_phone_desc: 'Please enter your new mobile number in the field below',
    email_added_successfully: 'The email has been added successfully',
    email_verified: 'The email has been verified successfully',
    phone_verified: 'The mobile number has been verified successfully',
    new_host_account: 'New Host Account',
    new_host_account_desc:
      'You have already registered as a host, in the following steps we will ask you to provide some additional information to register as a host.',
    account_info: 'Account Info',
    go_to_documentation: 'Go To Documentation',
    skip: 'Skip',
  },
  common,
  currency: {
    sar: 'SAR',
  },
  PropertyCard: {
    rating: 'Rating',
    perNight: '/night',
    total: 'Total',
  },
  favorite: {
    favorite: 'favorite',
    remove_from_wishlist: 'Removed from wishlist',
    add_to_wishlist: 'Added to wishlist',
    property_count: 'Number of properties',
    loginMessage:
      'Log in to view your favorites list, you can view the cities and properties added for each city once you log in',
    no_wishlist_cities_found:
      'Sorry, there are no favorite cities for you at the moment, add properties to your favorites in the cities that interest you to appear here and easily view them at any time!',
  },
  wishlist: {
    remove_from_wishlist: 'Removed from wishlist',
    add_to_wishlist: 'Added to wishlist',
  },
  validation: {
    required: '{field} is required',
    must_be_10_digits: 'Must be 10 digits',
    authentication: {
      name_min: '{field} must be at least two characters long',
      name_max: '{field} must be no more than 20 characters',
      email: 'Enter a valid email address (for example, <EMAIL>)',
      email_max: 'Email may not be more than 255 characters',
      invalid_number: 'phone number is invalid',
      only_letters: 'only alphabets are accepted',
      min_age: "You must be 18 years or older to use the Platform. Other people won't see your date of birth",
    },
    guest_commercial_registration: {
      commercial_name: 'Commercial name is required',
      commercial_registration_number: 'Commercial registration number is required',
      expiry_date: 'Expiry date is required',
    },
    tax_verification: {
      tax_number: 'Tax number is required',
      tax_address: 'Tax address is required',
      tax_image: 'Tax image is required',
    },
    no_properties_found: {
      no_properties:
        'Sorry, there are no properties matching the filter you selected. Try a different search filter or explore our other properties to discover various offers.',
      no_reservation:
        'Sorry, there are no current reservations in your account. Start now by exploring available properties and book your ideal stay.',
    },
    reserve: {
      cancel_message: 'You must provide a reason for canceling the reservation',
      cancel_reason: 'You must select a reason for canceling the reservation',
    },
    contact: {
      first_name_required: 'First name is required',
      last_name_required: 'Last name is required',
      email_invalid: 'Invalid email address',
      subject_required: 'Subject is required',
      message_min_length: 'Message should be at least 10 characters',
    },
    setting: {
      selectBookingMethod: 'Please select a booking method',
      selectPaymentMethod: 'Please select a payment method',
      minDownPayment: 'Down payment must be at least {min}%',
      maxDownPayment: 'Down payment cannot exceed {max}%',
      phoneNumberIdInvalid: 'Phone number ID must be a positive integer',
      phoneNumberRequired: 'Phone number is required',
      phoneCodeInvalid: 'Phone code must be a positive integer',
      checkInTimeRequired: 'Check-in time is required',
      checkOutTimeRequired: 'Check-out time is required',
      cancelPolicyRequired: 'Cancellation policy is required',
      insuranceAmountMin: 'Insurance amount must be at least {min}',
      selectPublishStatus: 'Please select a publication status',
      pendingDateRequired: "Pending date is required when status is set to 'date'",
      deleteReasonRequired: 'Please provide a reason for deletion',
      ruleIdInvalid: 'Rule ID is invalid',
      langCodeMin: 'Language code must be at least {min} characters',
      rulesMin: 'Rules must be at least {min} characters',
      rulesLangRequired: 'At least one language rule is required',
      rulesMinForLang: '{language} must be added',
      language: {
        ar: 'Arabic',
        en: 'English',
      },
    },
  },
  hostPages: {
    dashboard: 'Dashboard',
    my_properties: 'My Properties',
    my_reservations: 'Reservations',
    return_To_MyReservation: 'Return To MyReservation ',
    inbox: 'Inbox',
    account: 'Account',
    invoices: 'Invoices',
    administrative_accounts: 'Account Managers',
    wallet: 'Wallet',
    websites_for_my_real_estate_properties: 'Websites for my real estate properties',
    logout: 'Logout',
    actions: 'Actions',
    mobileAppAlertTitle: "🚧 We're not done with the mobile version yet!",
    mobileAppAlertDescription:
      'The host interface needs a slightly bigger screen 💻. Download the Rentoor app and always stay ready to manage your bookings and properties with ease and comfort.',
    dashboardPage: {
      statistics: 'Statistics',
      total_profit: 'Total Profit',
      available_balance: 'Available balance',
      number_of_reservations: 'Number of reservations',
      average_nights_reservation: 'Average Nights Reservation',
      nights: 'Nights',
      overview: 'Overview',
      average_reservation_amount: 'Average reservation amount',
      current_reservations: 'Current Reservations',
      reservations: 'Reservations',
      rating_rate: 'Rating Rate',
      The_following_rating_is_the_overall_average_rating_for_all_your_properties_and_reservations:
        'The following rating is the overall average rating for all your properties and reservations.',
      average_rating: 'Average rating',
      total_ratings: 'Total Ratings',
      positive_reviews: 'Positive reviews',
      negative_reviews: 'Negative reviews',
      indefinite: 'Indefinite',
      no_ratings: 'No Ratings',
      nothing_positive: 'Nothing positive',
      nothing_negative: 'Nothing negative',
    },
    myPropertiesPage,
    addNewPropertyPage,
    settingPropertyPage,
    payoutsPage,
    myRentoorWebsite,
    verifyPage,
    myReservationsPage: {
      now: 'Current',
      finished: 'Expired',
      the_visitor: 'The Visitor',
      visitor: 'Visitor',
      reservation_date: 'Reservatiom Date',
      reservation_status: 'Reservation Status',
      time__left: 'Time left',
      last_update: 'Last Update',
      search_by_guest_name_and_property_name: 'Search by property name',
      conversation: 'Chat',
      rejection_reasons: 'Rejection Reasons',
      noReservations: 'There are no current or pending reservations on your properties at the moment.',
      noFinishedReservations: 'There are no finished reservations on your properties at the moment.',
      accepted: 'Confirm',
      pre_accepted: 'Waiting for payment',
      pending: 'Pending',
      accept: 'Accept',
      reservation_decline: 'Reservation Decline',
      reservation_details: 'Reservation Details',
      confirm_payment: 'Confirm Payment',
      back_to_reservations: 'Back to My Reservations',
      title_confirm_invoice: 'Do you want to issue a cash invoice?',
      option_yes: 'Yes',
      option_no: 'No (If you have issued a manual invoice)',
      confirmation_message:
        'The amount has been confirmed and the invoice for the remaining amount has been successfully issued.',
      allow_payment: 'Allow the customer to pay the remaining amount via the platform',
      paid_amount: 'Amount Paid',
      remaining_amount: 'Remaining Amount',
      continue: 'Continue',
      confirmation_message_to_continue: 'Are you sure you want to proceed with this payment?',
      payment_confirmation: 'Payment has been confirmed successfully',
      go_to_chat_button: 'Go to the chat With ',
      discussـthisـreservation: 'Discuss This Reservation',
      after_sending_messages_or_contacting_support:
        'After sending messages or contacting support, you still need to accept or decline this reservation to avoid any penalties for delayed responses.',
      The_reservation_has_been_initially_approved:
        'The reservation has been initially approved and is awaiting payment for confirmation.',
      initial_approval_pending_payment_message: 'Initial approval granted, awaiting payment to confirm reservation',
    },
    accountPage: {
      profile: 'Profile',
      name_in_arabic: 'Name in Arabic',
      name_in_english: 'Name in English',
      id_number: 'ID number',
      phone_number: 'Phone Number',
      date_of_birth: 'Date Of Birth',
      search_country_placeholder: 'Search for a country',
      no_countries_found: 'No countries found',
      email: 'Email',
      gender: 'Gender',
      select_gender: 'select Gender',

      edit: 'Edit',
      verified_account: 'Verified account',
      not_verified_account: 'Not verified account',
      verify_Account: 'Verify Account',
      save_data: 'Save data',
      male: 'Male',
      female: 'Female',
      account_type: 'Account Type',
      facility: 'Facility',
      individual: 'Individual',
      facility_name: 'Facility name',
      commercial_registration_number: 'Commercial registration number',
      country_of_issue: 'Country of Issue',
      date_of_establishment_of_the_commercial_register: 'Date of establishment of the commercial register',
      commercial_image: 'Commercial Image',
      please_upload_the_register_photo: 'Please upload the register photo',
      if_you_or_your_establishment_are_registered_for_tax_please_enter_your_tax_information_here:
        'If you or your establishment are registered for tax, please enter your tax information here.',
      registered: 'registered',
      unregistered: 'unregistered',
      tax_number: 'Tax number',
      tax_address: 'Tax address',
      address_as_registered_in_tax: 'Address as registered in tax',
      vat_registration_certificate: 'VAT Registration Certificate',
      please_upload_the_image_of_the_tax_registration_certificate:
        'Please upload the image of the tax registration certificate.',
      statusMessages: {
        Pending:
          'The commercial registration details have been successfully updated! Thank you for updating your business information. The Rentoor team will review it as soon as possible.',
        Resubmitted:
          "We're sorry, the submitted commercial registration was not accepted due to: <span class='text-primary-500 cursor-pointer'>{reason}</span>. Please review the information, make the required changes, and resubmit it. The Rentoor team is here to support you!",
      },
      error401Mess:
        "Sorry, we couldn't verify the commercial registration information. Please try again or check the entered information.",
      dialog: {
        title: 'Your request has been received',
        message: {
          new_data:
            'Your changes have been saved successfully. We will review the submitted information within 24 hours.',
          existing_data:
            'The data has been submitted successfully. We will review the submitted information within 24 hours.',
          existing_data_ksa: 'The account details have been successfully updated',
        },
        Return_to_Account: 'Return to Account',
      },
      payouts: 'payouts',
      delete_account: 'Delete account',
      delete_account_warning:
        "We're sorry to see you go 😢 Are you sure you want to delete your account? Once confirmed, all your data will be deleted.",
      confirm_delete: 'Delete Account',
      select_reason: 'Please select a reason for deletion',
      other_reason_placeholder: 'Write the reason for deleting the account',
      other_reason_label: 'Please explain your reason for deleting the account',
      confirm_popup_title: 'Confirm account deletion',
      confirm_popup_message:
        'This action cannot be undone and all your data will be permanently deleted. Are you sure you want to delete your account?',
      cancel: 'Cancel',
      yes_delete: 'Yes, Delete My Account',
      success_message: '✅ Your account has been successfully deleted. We hope to see you again soon.',
      error_message: '❌ An error occurred while deleting your account. Please try again later.',

      reasons: {
        not_using: 'I no longer use the account',
        expensive: 'The service is too expensive',
        change_number: 'I want to change my phone number',
        confusing: "I don't understand how to use the service",
        not_available: 'The service is not available in my city',
        other: 'Other',
      },
      successMessages: {
        taxAndEstablishmentSuccess:
          'The tax and establishment data have been successfully saved and submitted, we will review the submitted information within 24 hours.',
        establishmentDataSuccess:
          'The data has been submitted successfully, we will review the submitted information within 24 hours.',
        accountDataSuccess: 'The account details have been successfully updated',
        establishmentUpdateSuccess:
          'The changes have been successfully saved, we will review the submitted information within 24 hours.',
      },
    },
    invoicesPage: {
      my_invoices: 'My Invoices',
      platform_invoices: 'Platform Invoices',
      search_by_guest_name_property_name: 'Search by property name',
      property_name: 'Property name',
      reservation_number: 'Reservation number',
      reservation_date: 'ٌReservation date',
      amount: 'Amount',
      invoice_type: 'Invoice type',
      actions: 'Actions',
      proforma: 'Proforma',
      tax: 'Tax',
      canceled: 'Canceled',

      search_property_name_placeholder: 'Search for property name',
      refund: 'Refund',
      tax_invoice: 'Tax Invoice',
      display: 'Display',
      print: 'Print',
      download: 'Download',
      share: 'Share',
      sort_invoices_by: 'Sort invoices by',
      latest: 'Latest',
      oldest: 'Oldest',
      save: 'save',
      filter_invoices: 'Filter Invoices',
      invoice_status_or_type: 'Invoice Status/Type',
      invoice_date: 'Invoice Date',
      from: 'From',
      to: 'To',
      apply: 'Apply',
      reset_all: 'Reset All',
      invoice_details: 'Invoice Details',
      back_to_invoices: 'Back to invoices',
      invoice_from: 'Invoice from',
      invoice_to: 'Invoice to',
      name: 'Name',
      address: 'Address',
      tax_number: 'Tax Number',
      invoice_number: 'Invoice Number',
      created_date: 'Created Date',
      due_date: 'Due Date',
      payment_way: 'Payment Way',
      amount_due: 'Amount Due',
      product: 'Product',
      quantity: 'Quantity',
      unit_price: 'Unit Price',
      total_before_tax: 'Total Before Tax',
      tax_percentage_symbol: 'Tax %',
      total_value: 'Total Value',
      commercial_number: 'Commercial Number',
      riyal: 'SAR',
      total_tax: 'Total Tax',
      total: 'Total',
      all: 'All',
      success_to_fetch_invoices: 'Invoices have been successfully retrieved',
      no_invoices: 'No invoices found',
      error_to_fetch_invoices: 'Failed to retrieve invoices',
      link_copied: 'Link copied',
      noInvoices:
        'Sorry, there are no invoices available in your account at this time. Invoices will be automatically generated when bookings or transactions occur. Follow your account to review them when available!',
    },
    administrative_accounts_page: {
      administrative_accounts: 'Accounts Managers',
      add_an_administrative_account: 'Add an account Manager',
      properties: 'properties',
      gust: 'Gust',
      please_select_the_addition_type: 'Please select the addition type',
      administrative_account_to_be_added: 'The account manager to be added',
      select_existing_admin: 'Select an existing account manager',
      mobile_number: 'Mobile number',
      invitation_status: 'Invitation status',
      search_property_name_placeholder: 'Search for property name',
      time_left: 'Time left',
      search_existing_admin: 'Search for an existing account manager',
      actions: 'Actions',
      pending: 'Pending',
      accepted: 'Accepted',
      finished: 'Finished',
      rejected: 'Rejected',
      edit: 'Edit',
      resend: 'Resend',
      display: 'Display',
      delete: 'Delete',
      not_found: 'Not Found',
      noAdministrativeAccounts:
        'There are no account managers added to your account yet. You can add a new account manager by clicking on the add button and start managing your properties on Rentoor easily.',
      assistant_director: 'Assistant Director',
      property_manager: 'Property Manager',
      back_to_administrative_account: 'back to account Manager',
      administrative_account_type: 'Account Manager Type',
      next: 'Next',
      send_invitation: 'Send Invitation',
      selectAdministrativeAccountType: 'Choose the type of account manager you want to add',
      roles: {
        manager: {
          title: 'Booking Manager',
          caption:
            'Will be able to manage bookings for assigned properties, including approving booking requests, rejecting, and canceling them. They will also be able to respond to messages and open disputes and add an account manager of type Booking Writer.',
          hint: 'Coming soon',
        },
        writer: {
          title: 'Booking Writer',
          caption:
            "Will be able to modify the availability of assigned properties on specific days to update the property's calendar in case of on-site bookings.",
          hint: 'Coming soon',
        },
      },
      adminAccount_info: 'Account Manager Information',
      addAdminAccountPrompt:
        'To add a new account manager, please enter the name and mobile number of the person you wish to invite as an account manager.',
      first_name: 'First Name',
      write_first_name: 'write First Name',
      write_sec_name: 'write Second Name',
      sec_name: 'Second Name',
      continue: 'continue',
      dialog: {
        successTitle: 'Invitation sent successfully',
        successMessage:
          'The invitation has been sent successfully, and the account managers list will be updated upon accepting the invitation',
      },
      add_property: 'Add Property',
      selected_Property: 'Selected Properties',

      name: 'Name',
      phone_number: 'Phone Number',
      status: 'Status',
      role: 'Role',
      delete_admin_account_title: 'Confirm Account Manager Deletion',
      delete_admin_account_message: 'Are you sure you want to delete this account manager?',
      instruction: 'Please select the properties you wish to grant access to.',
      all_properties: 'All properties and settings',
      success_edit_message: 'The account manager has been successfully updated',
      description_edit:
        'The account manager information has been successfully updated, and the list of account managers for the assigned properties has been refreshed.',
      manager_account_heading: 'The account manager to be added',
      choose_add_option_text: 'You can choose an already added account manager or invite a new person.',
      radio_option_old: 'Choose an already added account manager',
      radio_option_new: 'Add a new account manager',
      Please_select_the_properties_you_wish_to_grant_administrative_access:
        'Please select the properties you wish to grant manager access to.',
      You_can_select_multiple_properties_now_or_add_them_later:
        'You can select multiple properties now or add them later.',
      error_phone: {
        title: 'You cannot add this number',
        message:
          'This number is registered as a host account or as an account manager associated with another host account on the Rentor platform and cannot be added as an account manager linked to this account. Please use a new mobile number and try again.',
        button: 'Okay',
      },
      resend_success_title: 'Invitation Resent Successfully',
      resend_success_message:
        'The invitation has been resent successfully, and the account managers list will be updated once the invitation is accepted.',
    },
  },
  invoice_details: {
    invoice_details: 'Invoice details',
    proformaInvoice: 'Proforma invoice',
    taxInvoice: 'Tax invoice',
    canceledInvoice: 'Canceled invoice',
    refundInvoice: 'Refund invoice',
    invoice: 'Invoice',
    invoiceNumber: 'Invoice number',
    issueDate: 'Issue date',
    invoiceFrom: 'Invoice from',
    taxNumber: 'Tax number',
    invoiceTo: 'Invoice to',
    reservationDetails: 'Reservation details',
    reservation: 'Reservation',
    count: 'Count',
    total: 'Total',
    amountDue: 'Amount due',
    totalBeforeTax: 'Total before tax',
    totalTax: 'Total tax',
    reservationDate: 'Reservation date',
    reservationAmount: 'Reservation Amount',
    releaseDate: 'Release Date',
    show: 'Show',
    download: 'Download',
    share: 'Share',
    reservationNo: 'Reservation NO.',
    reservationInvoicesNo: 'Reservation invoices NO.',
  },
  reservation_details: {
    title: 'Reservation Details',
    cancelReason: 'Cancel Reason',
    declineReason: 'Decline Reason',
    booking_status: 'Booking Status',
    booking_number: 'Booking Number',
    remaining_time: 'Time Remaining for Approval',
    edit_booking: 'Edit',
    cancel_booking: 'Cancel',
    viewInvoice: 'View invoice',
    viewInvoices: 'View invoices',
    request_invoice: 'Request Invoice from Host',

    dates: {
      title: 'Dates',
      checkin_time: 'Check-in Time',
      checkout_time: 'Check-out Time',
    },
    guests_count: {
      title: 'Number of Guests',
    },
    nights_count: {
      title: 'Number of Nights',
    },
    location: {
      title: 'Location',
    },
    property_details: 'View Property Details',
    cancellation_policy: {
      title: 'Cancellation and Refund Policy',
      description:
        'Cancellations and refunds are allowed up to two days before check-in or modification/cancellation of some nights two days before the modified/cancelled night.',
    },
    price_details: {
      title: 'Price Details',
      price_per_night: 'Price (per night)',
      nights: ' {number} nights * {price} SAR',
      amount_already_paid: 'Amount Already Paid',
      remaining_amount: 'Remaining Amount',
    },
    total_amount: 'Total Amount',
    chat: 'chat',
    pay_now: 'pay now',
    rateReservation: 'Rate',
    reservationAgain: 'Reservation Again',
    dates_unavailable: 'Dates are unavailable',
    booking_cancelled: 'Your booking has been canceled due to',
    user_details: {
      member_since: 'Member since',
      age_years: 'Age {age} years',
      time_remaining_for_approval: 'Time remaining for approval',
      time_remaining_for_payment: 'Time remaining for payment',
    },
  },

  invoice_message: {
    with_unit:
      'Hello, please provide me with the invoice for reservation for {property}. Thank you for your cooperation.',
  },

  status: {
    Accepted: 'Confirmed',
    Pending: 'Waiting for approved',
    Rejected: 'Rejected',
    Cancelled: 'Canceled',
    Completed: 'Completed',
    Declined: 'Rejected',
    Expired: 'Expired',
    'Pre-Accepted': 'Waiting for payments',
    Pre_Approved: 'Waiting for payment',
  },
  cancelReservation: {
    reject: 'Reject',
    title: 'Cancel Reservation',
    reject_reservation: 'Reject Reservation',
    reject_reservation_message: 'Why do you want to reject the reservation?',

    description:
      'Please select a reason for cancellation. Note that cancellation fees may apply depending on the reason or if the customer wishes to file a dispute.',
    selectReason: 'Why do you want to cancel the reservation?',
    writeMessage: 'Write an additional message to the customer',
    messagePlaceholder: 'Enter a message for the customer here',
    confirmTitle: 'Confirm Cancellation',
    confirmMessage:
      'Are you sure you want to cancel this reservation? The host will be notified about the cancellation.',
    reservation_rejected_success: 'Reservation rejected successfully',
    reservation_cancelled_success: 'Reservation cancelled successfully',
  },
  filter_reservation: {
    title: 'Filter Reservations',
    propertyName: 'Property Name',
    propertyClassifications: 'Property Classifications',
    propertyUnits: 'Property Units',
    reservationStatus: 'Reservation Status',
    reservationDate: 'Reservation Date',
    from: 'From',
    to: 'To',
    search_property_name_placeholder: 'Search for property name',
    no_properties_found: 'No properties found.',
  },
  payment: {
    payment_failed: 'Payment Failed',
    payment_failed_desc: `The payment was not completed.Please check your payment details and try again.If the issue persists, please contact customer support for assistance.`,
    ok: 'OK',
  },
  notFound: {
    title: "Oops! Looks like you're lost 😅",
    description:
      "Don't worry, it happens to the best of us! The page you're looking for doesn't exist, but you can easily return home.",
    button: 'Back to Home',
  },
  staticPage,
  wallet,
  chat,
  notifications,
  seo: {
    home: {
      title: 'Rentoor – Smart Property Booking Platform in Saudi Arabia',
      description:
        'A licensed and approved platform by the Ministry of Tourism for booking and listing vacation homes (chalets, resorts, private villas, camps, rest houses, farms, caravans, apartments, and more).',
      keywords:
        'Rental properties, Short-term rentals, Long-term rentals, Best accommodations, Vacation homes, Apartments for rent, Booking, Travel, Home rentals, Homes for rent, Global destinations, Furnished rentals',
    },
    property: {
      title: 'Book Now on Rentoor - {propertyName}',
      description:
        'Discover your perfect property with Rentoor. Explore our extensive listings and find the ideal vacation home for your needs.',
      keywords:
        'Property details, Rentoor, Vacation homes, Property listings, Ideal vacation home, Explore properties, Find properties',
    },
    reserve: {
      title: 'Confirm Your Booking and Pay Securely | Rentoor',
      description:
        'Book your dream property with Rentoor. Enjoy a seamless booking experience and explore our wide range of vacation homes.',
      keywords:
        'Reserve, Book, Property, Vacation homes, Rentoor, Booking experience, Dream property, Seamless booking, Explore properties',
    },
    reservation: {
      title: 'My Reservations | Rentoor',
      description: 'Secure your stay with Rentoor. Book rooms easily and quickly.',
      keywords: 'Rentoor, Reservation, Room Booking, Stay, Travel',
    },
    account: {
      title: {
        profile: 'My Account Settings | Rentoor',
        accountType: 'Choose Account Type – Guest or Host | Rentoor',
        delete_account: 'Delete My Account | Rentoor',
        payouts: 'My Payouts | Rentoor',
      },
      description: 'Manage your account on Rentoor. Access your bookings, profile, and settings all in one place.',
      keywords: 'Account management, Rentoor, Profile settings, Bookings, User account',
    },
    inbox: {
      chats: 'My Inbox – Messages | Rentoor',
      notifications: 'My Notifications | Rentoor',
      description: 'Stay connected with Rentoor. Access your messages and notifications in one convenient inbox.',
      keywords: 'Inbox, Rentoor, Messages, Notifications, Communication',
    },
    favorite: {
      title: 'My Favorite Properties | Rentoor',
      title_favorite_city: 'Favorite Rentals in {cityName} | Rentoor',
      description: 'Save your favorite properties on Rentoor. Easily access and manage your favorite listings.',
      keywords: 'Favorite properties, Rentoor, Saved listings, Property management',
    },
    search: {
      title: 'Search Vacation Rentals Across Saudi Arabia | Rentoor',
      description:
        'Discover your perfect property with Rentoor. Explore our extensive listings and find the ideal vacation home for your needs.',
      keywords:
        'Property details, Rentoor, Vacation homes, Property listings, Ideal vacation home, Explore properties, Find properties',
    },
    verify: {
      title: 'Rentoor | Verify Your Account',
      description: 'Verify your account on Rentoor to unlock all features and enjoy a seamless booking experience.',
      keywords: 'Account verification, Rentoor, Unlock features, Booking experience',
    },
    about: {
      title: 'About Rentoor – Your Trusted Rental Partner',
      description:
        'Rentoor is an innovative real estate rental platform that provides you with fast and easy solutions for renting and leasing properties safely and comfortably.',
      keywords: 'About Rentoor, Rentoor information, Property rental, Booking platform',
    },
    protection: {
      title: 'Rentoor Host Protection Program',
      description:
        'We provide you with complete security and protection when renting out your property through Rentoor, with strong guarantees and advanced safety solutions.',
      keywords: 'Host protection, Rental security, Safe rentals, Risk-free property rental',
    },
    becomeHost: {
      title: 'List Your Property – Become a Rentoor Host',
      description:
        'Start your journey as a host on Rentoor, list your property for rent, and earn extra income easily and securely.',
      keywords: 'Become a host, Rent a property, Rentoor host, Earn money from rentals',
    },
    contactUs: {
      title: 'Contact Us | Rentoor',
      description:
        'Need help? Contact the Rentoor support team for quick answers and effective solutions to all your inquiries.',
      keywords: 'Contact Rentoor, Rentoor support, Get in touch with Rentoor, Customer support',
    },
    host: {
      title: 'Host Dashboard | Easily Manage Your Properties with Rentoor',
      description:
        'Manage your bookings, track your earnings, and control your properties effortlessly through the Rentoor Host Dashboard.',
      keywords: 'Host dashboard, Property management, Booking tracking, Rentoor host',
      myProperties: {
        title: 'My Properties | Rentoor',
        description:
          'Manage your properties, track your earnings, and control your properties effortlessly through the Rentoor Host Dashboard.',
        keywords: 'My properties, Property management, Booking tracking, Rentoor host',
      },
      invoices: {
        title: 'My Invoices | Rentoor',
        description:
          'Manage your invoices, track your earnings, and control your properties effortlessly through the Rentoor Host Dashboard.',
        keywords: 'Invoices, Invoice management, Booking tracking, Rentoor host',
      },
      addNewProperty: {
        title: 'Add New Property | Rentoor',
        no_title: 'Edit Property Draft No. {propertyId} | Rentoor',
        copyProperty: 'Copy Property – {propertyName} | Rentoor',
        addNewClassification: 'Add New Classification | Rentoor',
        copyClassification: 'Copy Classification – {propertyName} | Rentoor',
        description: 'Add a new property to Rentoor to list it for rent and generate income.',
        keywords: 'Add new property, Rentoor, List properties, Rent properties',
      },
      classification: {
        title: 'My Property Classifications | Rentoor',
        edit: 'Edit Classification – {propertyName} | Rentoor',
        manage: 'Manage Classification – {propertyName} | Rentoor',
        show: 'Show Classification – {propertyName} | Rentoor',
        description: 'Manage the classifications of your properties and list them for rent to generate income.',
        keywords: 'Classifications, Rentoor, List properties, Rent properties',
      },
      editProperty: {
        title: 'Edit Property – {propertyName} | Rentoor',
        description: 'Edit the details of your property and list it for rent to generate income.',
        keywords: 'Edit property, Rentoor, List properties, Rent properties',
      },

      settingProperty: {
        title: 'Property Settings {classificationId} | Rentoor',
        manage: 'Manage Property – {propertyName} | Rentoor',
        show: 'Show Property – {propertyName} | Rentoor',
        edit: 'Edit Property – {propertyName} | Rentoor',
        description: 'Manage the settings of your property and list it for rent to generate income.',
        keywords: 'Property settings, Rentoor, List properties, Rent properties',
      },
      myReservations: {
        title: 'My Reservations | Rentoor',
        title_reservation: 'Reservation Details – {reservationName} | Rentoor',
        description:
          'Manage your reservations, track your earnings, and control your properties effortlessly through the Rentoor Host Dashboard.',
        keywords: 'My reservations, Reservation management, Booking tracking, Rentoor host',
      },
      myRentoorWebsite: {
        title: 'My Rentoor Websites | Rentoor',
        description: 'Manage your Rentoor websites and list your properties for rent to generate income.',
        keywords: 'My Rentoor websites, Rentoor, List properties, Rent properties',
      },
      addRentoorWebsite: {
        title: 'Add New Rentoor Website | Rentoor',
        add_website_to_property: 'Setup Website for {propertyName} | Rentoor',
        description: 'Add a new Rentoor website to list your properties for rent and generate income.',
        keywords: 'Add new Rentoor website, Rentoor, List properties, Rent properties',
      },
      wallet: {
        title: 'My Wallet | Rentoor',
        description:
          'Manage your wallet, track your earnings, and control your properties effortlessly through the Rentoor Host Dashboard.',
        keywords: 'My wallet, Wallet management, Booking tracking, Rentoor host',
      },
      administrativeAccounts: {
        title: 'Account Managers | Rentoor',
        title_specific: 'Account Managers for Property {propertyName} | Rentoor',
        description:
          'Manage your administrative accounts, track your earnings, and control your properties effortlessly through the Rentoor Host Dashboard.',
        keywords: 'My administrative accounts, Administrative accounts management, Booking tracking, Rentoor host',
      },
      addNewAdministrativeAccounts: {
        title: 'Add New Account Manager | Rentoor',
        title_specific: 'Add New Account Manager for Property {propertyId} | Rentoor',
        description: 'Add a new account manager to manage your properties and bookings.',
        keywords: 'Add new account manager, Rentoor, Manage properties, Manage bookings',
      },
    },
    termsAndConditions: {
      title: 'Terms and Conditions | Rentoor',
      description:
        "The full terms and conditions for using Rentor's property rental services: A comprehensive guide to responsible and transparent renting with Rentor.",
      keywords:
        'property rental, rental terms, rental conditions, property rental services, responsible renting, transparent renting, property booking, property rental platform, safe and smooth renting, best rental experience, tenant and landlord rights, rental obligations, online property rental, rental booking terms, Rentor property rental',
    },
    privacyPolicy: {
      title: 'Privacy Policy | Rentoor',
      description:
        'Rentoor is committed to protecting your privacy. Read our privacy policy to learn how we collect, use, and safeguard your information.',
      keywords:
        'privacy policy, data protection, user privacy, information security, Rentoor privacy, data collection, data usage, user rights',
    },
    termsOfUse: {
      title: 'Terms of Use | Rentoor',
      description:
        "The full terms and conditions for using Rentor's property rental services, a comprehensive guide to ensuring a safe and transparent rental experience for all parties.",
      keywords:
        'property rental terms of use, rental conditions, property rental services, responsible renting, transparent renting, property booking, property rental platform, safe and smooth renting, rental experience, rental obligations, online property rental, rental booking terms, Rentor property rental',
    },
  },
  share: {
    title: 'Share this Ad via:',
    message:
      "📢 I found an amazing property on Rentoor! 🏡✨\nIf you're looking for a suitable place, don't miss out on this offer! Check the details and book easily before it's gone 👇\n\n#Rentoor #EasyBookings #Property #ComfortableExperience\n",

    emailMessage:
      "Hello,\n\nI found an amazing property on Rentoor and thought you might like it!\n\nIf you're looking for a special place, this property is a great choice! Check it out and let me know what you think.\n\nBest regards,\n{userName}\n\nYou can view the details and book easily through the link:\n",
    propertyLink: 'Property Link',
    hashtags: '#Rentoor #EasyBookings #Property #ComfortableExperience',
  },
} as const;
