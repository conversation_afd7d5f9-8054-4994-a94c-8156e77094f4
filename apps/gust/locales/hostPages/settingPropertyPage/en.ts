export default {
  show: 'Show Property',
  manage: 'Manage Property',
  edit: 'Edit Property',
  show_classifications: 'Show Classification',
  manage_classifications: 'Manage Classification',
  edit_classifications: 'Edit Classification',
  manage_unit_label: 'Manage Unit',
  returnToMyProperties: 'Return to my properties',
  returnToManage_classifications: 'Return to Manage Classification',
  returnToManage_property: 'Return to Manage property',
  returnToEdit_property: 'Return to ِEdit property',
  myProperties: 'My properties',
  bookingMethods: 'Booking Methods',
  paymentMethod: 'Payment Method',
  adminAccounts: 'Account Manager',
  notificationId: 'Notification Number',
  checkInOut: 'Check-In & Check-Out',
  cancellationPolicy: 'Cancellation Policy',
  insuranceAmount: 'Insurance Amount',
  additionalTerms: 'Additional Terms',
  propertyPublish: 'Suspend Publishing',
  propertyUnPublish: 'Cancel Publishing',

  propertyDelete: 'Delete Property',
  bookingOptions: {
    instantBooking: {
      label: 'Instant Booking',
      recommended: 'Recommended',
      description: 'Guests will book instantly and pay the full amount upfront',
    },
    requestBooking: {
      label: 'Booking Request',
      description:
        'The booking request will be sent, and the booking will only be confirmed after host approval and guest prepayment',
    },
    instruction:
      'Please choose at least one booking method suitable for property management and provide appropriate options for different guests',
    available_booking_methods: 'Available booking methods',
  },
  paymentOptions: {
    fullPay: {
      label: 'Pay through platform',
      recommended: 'Recommended',
      description:
        'The guest will pay in full at the time of booking through one of the available payment methods on the platform, and platform fees will be deducted',
    },
    partialPay: {
      label: 'Pay partially through platform',
      description:
        'Choose the advance payment percentage (25% to 90%) which will be paid through the platform. The platform is not responsible for the remaining amount that is paid directly from the guest to the host. The host will have to collect the remaining payment from the guest',
    },
    PartialPayOrFullPay: {
      label:
        'Give the guest the choice of either paying a down payment through the platform and the remainder in cash upon check-in, or paying in full through the platform',
      description:
        'Please select the percentage of the down payment (from 25% to 90%) that will be paid through the platform. The platform is not responsible for the remaining amount paid directly in cash from the guest to the host. The guest will choose to pay a down payment or the full amount',
    },
    percentageLabel: 'Select advance payment percentage',
    percentageValue: '{{value}}%',
  },
  insurance_amount: {
    title: 'Insurance Amount',
    optional_note: 'If applicable',

    description:
      'The amount is taken directly from the tenant upon check-in to ensure the property is handed over without any damage.',
  },
  mobile_number_info:
    'This is the only mobile number that will receive booking requests, booking notifications, and customer inquiries for this property',
  another_number: 'Another number :',
  same_number_linked_to_account: 'The same number linked to my account',

  suspendUntilDate: 'Suspend until a date',
  suspendIndefinitely: 'Suspend indefinitely',
  propertyPublishDescription:
    'This option allows you to temporarily suspend the publishing of your property. Please note that suspending the property does not exempt you from paying fees during the suspension period. To avoid paying fees, you need to delete the property..',
  saveChanges: 'Save Changes',
  dialogMessage: 'You have unsaved changes on',
  sureMoveWithoutSave: ', Are you sure you want to proceed without saving the changes?',
  propertyUnlistedIndefinite: 'This property is suspended indefinitely.',
  propertyUnlistedUntil: 'This property is suspended until.',
  republishProperty: 'Republish Property',
  save: 'Save',
  settings: 'Settings',
  move: 'Proceed',
  delete: 'Delete',
  prices: 'Price',
  additional_data_in_arabic: 'Additional Terms in Arabic',
  additional_data_in_english: 'Additional Terms in English',
  additionalLanguageConditions: 'Enter conditions in an additional language',
  ifAny: 'if any',
  calender: 'Calender',
  continue: 'Continue',
  updateSuccess: 'Information updated successfully',
  check_in_title: 'Check-in Time',
  check_out_title: 'Check-out Time',
  check_out_error:
    'The selected check-out time is not recommended. The commonly accepted check-out time is 9:00 AM or later.',
  priceTap: {
    pricesByWeekday: 'Prices by weekday ',
    pricesBySpecificDates: 'Prices by specific dates',
    discountsAndOffers: 'Discounts and offers',
  },
  weekday_price_selector: {
    select_weekdays: 'Choose days of MidWeek',
    weekday_price_label: 'Midweek Price',
    select_weekend_days: 'Choose days of the week',
    weekend_price_label: 'Price for Weekend Days',
  },
  weekdays: {
    1: 'Sunday',
    2: 'Monday',
    3: 'Tuesday',
    4: 'Wednesday',
    5: 'Thursday',
    6: 'Friday',
    7: 'Saturday',
  },
  you_can_manage_discount:
    'You will be able to manage discounts and special offers for this property from this page and they will apply to all classifications and units under it. To control night prices please manage them from the classification management page for each classification separately.',
  you_can_manage_available_multi_classification:
    'You will be able to manage the available booking dates for this property from this page and it will apply to all its categories and units. To control the calendar for the unit, please manage it from the unit management page for the unit.',
  you_can_manage_available_multi_unit:
    'You will be able to manage the available booking dates for this property from this page and it will apply to all its units. To control the calendar for the unit, please manage it from the unit management page for the unit.',
  unit_calender_note:
    'The unit calendar is limited to adjusting unit availability. To control prices, please use the classification calendar or the main property calendar.',
  discounts_and_offers: {
    discounts_and_offers: 'Discounts and offers',
    discount_note:
      'Note: If multiple discounts are applicable for the same day, the highest discount will be applied and the discounts will not be aggregated.',
    discount_all_nights: 'Discount all nights',
    discount_all_weekdays: 'Discount applies to all days of the week',
    sar: 'SAR',
    discount_weekend: 'Weekend discount',
    discount_weekend_note: 'Discount applies to weekends specified in the price list by weekday.',
    discount_by_nights: 'Discount by nights',
    discount_by_nights_note:
      'Discount applied according to the number of nights booked, for example, the more nights, the greater the discount',
    add: 'Add',
    number_of_days: 'No. of days',
    input_add_discount_error: 'Enter discount and number of days',
    early_booking: 'Early Booking',
    early_booking_note:
      'Discount applies if the property is booked a certain number of days before the booking date (allowed value from 30 to 1080 days)',
    late_booking: 'Late Booking',
    late_booking_note:
      'Discount applied if the property is booked within a certain number of days from the booking date (allowed value from 1 to 28 days)',
    discount_must_be_from_0_to_99: 'Discount must be from 0 to 99',
    early_booking_must_between_30_and_1080_days: 'Early booking must be between 30 and 1080 days',
    late_booking_must_between_1_and_28_days: 'Late booking must be between 1 and 28 days',
    discount_deleted_successfully: 'Discount deleted successfully',
    by_night_must_between_1_and_99_days: 'Length of stay must be between 1 to 99 days.',
    discount_is_required: 'Discount is required',
    number_of_days_required: 'No. of days is required',
    discount_placeholder: 'Enter discount from 0 to 99',
    _0_to_99: '0 to 99',
  },
  pricesByCalender: {
    price_per_night: 'Price per night',
    from: 'From',
    to: 'To',
    sar: 'SAR',
    delete_selected_dates: 'Delete selected dates',
    apply: 'Apply',
    select_dates_first: 'Please select dates',
    enter_price_per_night: 'Enter price per night',
    price_updated_successfully: 'Price updated successfully',
    error_in_updating_price: 'Error in updating price',
    price_must_be_greater_than_or_qual_to_zero: 'Price must be greater than or qual to zero',
    price_must_be_less_than_2147483647: 'Price must be less than 2147483647',
    selected_date_not_available: 'Selected date not available',
    select_valid_dates: 'Please select valid dates',
  },
  calenderTap: {
    property_availability_period: 'Property Availability Period',
    availability_by_specific_dates: 'Availability by specific dates',
    sync_calendar_with_other_sites: 'Sync calendar with other sites',
    unavailable: 'Unavailable',
    booked: 'Booked',
    available: 'Available',
    selected_period_status: 'Selected period status',
    available_list: 'Available',
    unavailable_list: 'Unavailable',
    from: 'From',
    to: 'To',
    sar: 'SAR',
    delete_selected_dates: 'Delete selected dates',
    apply: 'Apply',
    select_dates_first: 'Please select dates',
    select_availability_state_first: 'Please select availability state',
    updated_successfully: 'Updated successfully',
    error_in_updating: 'Error while updating',
    save: 'Save',
    save_changes: 'Save changes',
    you_dont_save_changes: "You don't save changes on",
    are_you_sure: ', are you sure you want to move without saving changes?',
    move: 'Move',
    calender: 'Calender',
  },
  propertyAvailabilityPeriod: {
    property_availability_period: 'Property Availability Period',
    you_can_select_a_period: 'You can select a period for this property to book.',
    note: 'Note',
    change_period_note: 'The change will not affect existing reservations or manually adjusted availability settings.',
    availability_from: 'Availability from its date',
    availability_from_to: 'Your property will be available for booking from ',
    to: 'to',
    _3_months: '3 months',
    _6_months: '6 months',
    _9_months: '9 months',
    _12_months: '12 months',
    _3_years: '3 years',
    save: 'Save',
  },
  middle_days_error: 'At least one day must remain in middle days.',
  weekend_prices_error: 'At least one day must remain in weekend prices.',
  syncCalendarWithOtherSites: {
    unit_calendar_link: 'Unit Calendar Link',
    unit_calendar_link_note:
      "You can add the following link to other booking sites like Airbnb to sync their calendar with Rentor's calendar to avoid booking conflicts. If you want to sync more than two sites please sync all sites to only one site to avoid sync overlap.",
    link_copied: 'Link copied',
    calender_synced_with: 'External calendars that have been synched.',
    sync_with_other_apps: 'Sync with other apps',
    save: 'Save',
    app_name: 'App Name',
    calender_link: 'Calender Link',
    last_update_time: 'Last Update Time',
    actions: 'Actions',
    empty_table_calender:
      'You can easily sync other calendars with our system! Start by adding an external calendar link to track availability across all platforms effortlessly.',
    add_sync_calender: 'Add a new calendar sync',
    add_new_calender: 'Add a new calendar',
    your_calender_name: 'Calendar Name',
    your_calender_title: 'Calendar Link',
    add: 'Add',
    calender_name_placeholder: 'Calendar Name',
    calender_title_placeholder: 'Calendar Link',
    url_is_not_valid: 'The url is not valid',
    url_is_too_long: 'The url is too long, it must be less than 255 characters',
    name_is_too_long: 'The name is too long, it must be less than 255 characters',
    this_field_is_required: 'This field is required',
    add_calender_success: 'Calendar added successfully',
    calender_deleted: 'Calendar deleted successfully',
    calender_deleted_failed: 'Calendar delete failed',
    calender_synced: 'Calendar synced successfully',
    calender_synced_failed: 'Calendar sync failed',
  },
  value_must_be_from_0_to_99: 'Value must be from 0 to 99',
  classifications: {
    classifications: 'Classifications',
    units_details: 'Units details',
    manage: 'Manage',
    save: 'Save',
    studio_suite: 'Studio Suite',
    deluxe_suite: 'Deluxe Suite',
    luxury_diplomatic_suite: 'Luxury Diplomatic Suite',
    two_bedroom_suite: 'Two Bedroom Suite',
  },
  units: {
    units: 'Units',
    units_details: 'Units details',
    manage: 'Manage',
    save: 'Save',
    unit_no: 'Unit No.',
    this_feature_is_coming_soon: 'This feature is coming soon',
    great_job_you_will_be_able_to_manage_the_units_after_rentoor_team_approves:
      'Great job! You will be able to manage the units after Rentoor team approves this classification.',
    please_complete_the_classification_details_before_you_can_manage_units:
      'Please complete the classification details before you can manage units.',
  },
  cancellationPolicyTab: {
    recommended: 'Recommended',
    notRecommended: 'Not recommended',
  },
  edit_classification: 'Edit Classification',

  manage_unit: {
    availability_by_specific_dates: 'Availability by specific dates',
    sync_calendar_with_other_sites: 'Sync calendar with other sites',
    settings: 'Settings',
    calender: 'Calender',
    classification: 'Classification',
    classification_note: 'Note: Please save your changes before moving on to manage the classification.',
    not_found: 'Not found',
    save: 'Save',
    classification_updated_successfully: 'Classification updated successfully',

    save_changes: 'Save changes',
    you_dont_save_changes: "You don't save changes on",
    are_you_sure: ', are you sure you want to move without saving changes?',
    move: 'Move',
  },

  please_contact_support_to_edit_or_manage: 'Please, contact Support to edit or manage',
  select_phone_number: 'Select phone number',
  searchPlaceholder: 'Search phone number',
};
