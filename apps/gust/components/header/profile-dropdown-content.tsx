'use client';
import { useCallback, useEffect, useState, type FC, type ReactElement } from 'react';
import { DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import IconWrapper from '@/components/icons';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { usePathname, useRouter } from 'next/navigation';
import ImgWithBluredBG from '../common/ImgWithBluredBG';
import { useUserStore } from '@/store/useUserStore';
import AuthBtn from '../authentication/auth-btn';
import useHandleLogout from '@/hooks/use-handle-logout';
import useNotificationsCountHandler from '@/hooks/useNotificationsCountHandler';

const ProfileDropdownContent: FC<any> = (): ReactElement => {
  const t = useScopedI18n('header');
  const { userData, isGetProfilePending } = useUserStore((state) => state);
  const router = useRouter();
  const { handleLogoutClick, isLoading } = useHandleLogout();
  const pathname = usePathname();
  const isHost = pathname.includes('host');
  const [hasError, setHasError] = useState(false);
  const { notificationsCount } = useNotificationsCountHandler();

  useEffect(() => {
    setHasError(false); // Reset error state when image changes
  }, [userData?.profile_picture]);

  const handleToRoute = useCallback(
    (link: string) => {
      router.push(link);
    },
    [router.push]
  );

  return (
    <DropdownMenuContent align="end" className="w-60">
      {!userData || isGetProfilePending ? (
        <AuthBtn asMenuItem />
      ) : (
        <>
          <div className="flex flex-row gap-3 px-4 pb-3 pt-4">
            {userData?.profile_picture && !hasError ? (
              <ImgWithBluredBG
                src={userData?.profile_picture}
                width="52px"
                height="52px"
                onError={() => setHasError(true)}
                className="rounded-full border-[2px] border-solid border-[#fff]"
              />
            ) : (
              <IconWrapper className="text-gray-100" name="ProfileCircle" size={52} variant="Bold" />
            )}

            <div className="flex flex-col">
              <p className="md-text">
                {userData?.first_name} {userData?.last_name}
              </p>
              <p className="sm-text text-black-200">{userData?.phone_number_full}</p>
            </div>
          </div>
          {!isHost && (
            <>
              <DropdownMenuItem
                onClick={() => handleToRoute('/account')}
                className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start">
                <IconWrapper className="text-secondary icon-wrapper" name="UserSquare" size="24" variant="Outline" />
                {t('profile')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleToRoute('/chat')}
                className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start">
                <IconWrapper className="text-secondary icon-wrapper" name="Message" size="24" variant="Outline" />
                {t('chats')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleToRoute('/chat?tab=notifications')}
                className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start">
                <IconWrapper className="text-secondary icon-wrapper" name="Notification" size="24" variant="Outline" />
                <div className="flex flex-1 items-center justify-between">
                  <span>{t('notifications')}</span>
                  {Boolean(notificationsCount) && (
                    <span className="text-primary-300 bg-primary-50 rounded-full px-2 py-0.5 text-sm">
                      {notificationsCount}
                    </span>
                  )}
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleToRoute('/reservation?type=now')}
                className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start">
                <IconWrapper className="text-secondary icon-wrapper" name="CalendarIcon" size="24" variant="Outline" />
                {t('reservation')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleToRoute('/wishList')}
                className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start">
                <IconWrapper className="text-secondary icon-wrapper" name="Heart" size="24" variant="Outline" />
                {t('favorite')}
              </DropdownMenuItem>
            </>
          )}
        </>
      )}
      <DropdownMenuSeparator />
      <DropdownMenuItem
        className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start"
        onClick={() => router.push('/contact')}>
        <IconWrapper className="text-secondary icon-wrapper" name="Sms" size="24" variant="Outline" />
        {t('contact')}
      </DropdownMenuItem>
      {userData ? (
        <DropdownMenuItem
          isLoading={isLoading}
          className="focus:text-primary-300 dropdown-menu-item cursor-pointer justify-start gap-3 px-4 py-3 text-start"
          onClick={handleLogoutClick}>
          <IconWrapper className="text-secondary icon-wrapper" name="Logout" size="24" variant="Outline" />
          {t('logout')}
        </DropdownMenuItem>
      ) : null}
    </DropdownMenuContent>
  );
};

export default ProfileDropdownContent;
