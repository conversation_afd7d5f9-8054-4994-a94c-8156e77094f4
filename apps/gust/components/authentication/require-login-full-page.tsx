import { FC } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from '../ui/card';
import { Button } from '../ui/button';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import Link from 'next/link';
import { ROUTES } from '@/constants';
import FailAlertIcon from '../icons/fail-alert-icon';

interface RequireLoginFullPageProps {
  description: string;
}

const RequireLoginFullPage: FC<RequireLoginFullPageProps> = ({ description }) => {
  const t = useScopedI18n('authentication');
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="flex items-center justify-center">
        <FailAlertIcon />
      </div>
      <div className="mt-4">
        <p className="text-secondary-200 text-md text-center">{description}</p>
        <div>
          <Link className="d-block flex items-center justify-center" href={ROUTES.LOGIN}>
            <Button className="mt-4 min-w-40 text-lg" variant="default">
              {t('login')}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RequireLoginFullPage;
