'use client';
import { useEffect, useState, type FC } from 'react';
import Header from '@/components/header/desktop-header';
import HostPagesSidebar from '../../hostPages/sidebar';
import styles from './styles.module.scss';
import { cn } from '@/lib/utils';

type LayoutProps = {
  children: React.ReactNode;
};

const HostLayout: FC<LayoutProps> = ({ children }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const onToggleCollapse = () => {
    const newVal = !isCollapsed;
    localStorage.setItem('hostSidebarCollapsed', JSON.stringify(Number(newVal)));
    setIsCollapsed(newVal);
  };

  useEffect(() => {
    const isCollapsed = !!Number(localStorage.getItem('hostSidebarCollapsed'));
    if (isCollapsed) {
      setIsCollapsed(isCollapsed);
    }
  }, []);

  return (
    <div className={styles.hostLayoutContainer}>
      <Header details className="z-10 px-6" fullWidth />

      <div className="flex bg-[#fff]" style={{ height: 'calc(100vh - 80px)' }}>
        <HostPagesSidebar {...{ isCollapsed, onToggleCollapse }} />

        <main
          className="flex-1"
          style={{
            marginInlineStart: isCollapsed ? 85 : 240,
            transition: 'all .3s ease-in-out',
            overflow: 'auto',
          }}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default HostLayout;
