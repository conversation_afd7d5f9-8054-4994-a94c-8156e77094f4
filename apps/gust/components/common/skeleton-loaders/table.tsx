import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';

const cellClasses = 'p-2';
const skeletonItemClasses = 'flex-1 rounded-[12px] bg-gray-200';

const TableSkeletonLoader = ({ cols = 4, rows = 4 }: { cols?: number; rows?: number }) => {
  const colsLength = Array(cols).fill(0);
  const rowsLength = Array(rows).fill(0);

  return (
    <Table className="table-auto">
      <TableHeader>
        <TableRow className="hover:bg-gray-50">
          {colsLength.map((_, index) => (
            <TableHead key={index} className={cellClasses}>
              <Skeleton className={cn(skeletonItemClasses, 'h-[40px]')} />
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>

      <TableBody>
        {rowsLength.map((num, index) => {
          return (
            <TableRow key={index}>
              {colsLength.map((_, index) => (
                <TableCell key={index} className={cellClasses}>
                  <Skeleton className={cn(skeletonItemClasses, 'h-[60px]')} />
                </TableCell>
              ))}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};

export default TableSkeletonLoader;
