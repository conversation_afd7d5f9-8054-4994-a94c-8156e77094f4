'use client';
import type { FC, ReactElement } from 'react';
import React from 'react';
import Link from 'next/link';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import IconWrapper from '@/components/icons';
import { cn } from '@/lib/utils';
import Logo from '@/components/common/logo';
import AppleApp from '@/public/assets/Mobile_app_store_badge.png';
import GooglePlay from '@/public/assets/Google_Play.png';
import { Separator } from '../ui/separator';
import { usePathname } from 'next/navigation';
import { ROUTES } from '@/constants';
import Image_Backend from '../common/Image_backend';
import { useGetFooterData } from '@/queries/common';
import CompanyCommercialImage from '@/public/assets/company_commercial_image.svg';

const Footer: FC<{ className?: string }> = ({ className = '' }): ReactElement => {
  const t = useScopedI18n('footer');
  const pathname = usePathname();
  const isSearch = pathname.includes('search');
  const isWishList = pathname.includes('wishList');
  const isChat = pathname.includes('chat');

  const { data } = useGetFooterData();
  const company_commercial_number = data?.data?.data?.find((item) => item.name === 'company_commercial_number')?.value;

  const handleGoToExLink = (link: string) => {
    window.open(link);
  };

  return (
    <div className={cn(isSearch || isWishList || isChat ? '' : `mt-14  w-full`, className)}>
      <div className="border-gray-75 bg-white-75 flex flex-col border-t-[0.6px] pb-10 pt-16">
        <div className="container-layout mb-14 flex flex-col items-center justify-between gap-8 md:flex-row">
          <div className="max-w-46 flex flex-col gap-10">
            <Logo />
            <p className="text-lg/8 text-gray-400">{t('description')}</p>
            <div className="flex flex-wrap items-center gap-8 md:flex-row md:justify-between md:gap-0">
              <Link className="text-secondary  hover:text-primary text-lg font-bold" href={ROUTES.ABOUT_US} scroll>
                {t('about')}
              </Link>
              <Link className="text-secondary hover:text-primary text-lg font-bold" href={ROUTES.BECOME_HOST} scroll>
                {t('become_host')}{' '}
              </Link>
              <Link className="text-secondary hover:text-primary text-lg font-bold" href={'/protectionHost'} scroll>
                {t('host_protection')}{' '}
              </Link>

              <Link className="text-secondary hover:text-primary text-lg font-bold" href={ROUTES.CONTACT_US} scroll>
                {t('help_center')}{' '}
              </Link>
              <Link className="text-secondary hover:text-primary text-lg font-bold" href="#" scroll>
                {t('cancellation_options')}{' '}
              </Link>
            </div>
          </div>
          <div className="flex flex-col gap-5">
            <p className="text-secondary text-lg  font-semibold">{t('download')}</p>
            <Image_Backend
              alt="apple-app"
              className="w-full cursor-pointer"
              onClick={() => handleGoToExLink('https://www.google.com')}
              height={40}
              src={AppleApp}
            />
            <Image_Backend
              onClick={() => handleGoToExLink('https://www.google.com')}
              alt="Google-app"
              className="w-full cursor-pointer"
              height={40}
              src={GooglePlay}
            />

            {Boolean(company_commercial_number) && (
              <div className="border-secondary-300 text-secondary-300 flex max-w-[180px] items-center gap-1 rounded border border-solid p-2 text-[8px] font-bold">
                <Image_Backend alt="company_commercial_number" height={28} src={CompanyCommercialImage} />

                <div className="flex flex-col">
                  {t('company_commercial_label')}
                  {company_commercial_number}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="container-layout">
          <Separator className="bg-black-50 my-2" />
          <div className=" mb-4 flex flex-col items-center justify-between gap-8 md:mt-8 md:flex-row">
            <div className="flex items-center justify-between gap-7">
              <Link className="md-text text-gray-400" href="/policy">
                {t('privacy-policy')}
              </Link>
              <Link className="md-text text-gray-400" href="/terms-conditions">
                {t('terms-of-use')}
              </Link>
              <Link className="md-text text-gray-400" href="/terms-use">
                {t('copyright-policy')}
              </Link>
            </div>
            <div className="flex items-center justify-between gap-5">
              <Link href="https://www.facebook.com/tahwool" target="_blank">
                <IconWrapper className="text-secondary " name="Facebook" size={24} variant="Bold" />
              </Link>
              <Link href="https://www.linkedin.com/company/tahwool/posts/?feedView=all" target="_blank">
                <IconWrapper className="text-secondary" name="LinkedIn" size={24} />
              </Link>
              <Link href="https://x.com/tahwool" target="_blank">
                <IconWrapper className="text-secondary" name="XIcon" size={24} />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Footer;
