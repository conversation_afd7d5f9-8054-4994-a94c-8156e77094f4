import type { IconProps } from 'iconsax-react';
import type { FC, ReactElement } from 'react';
import {
  ArrowLeft2,
  <PERSON>Right2,
  <PERSON>Up,
  ArrowUp2,
  ArrowDown2,
  Calendar,
  Global,
  Heart,
  HambergerMenu as BurgerMenu,
  ProfileCircle,
  Sms,
  SearchNormal,
  Routing,
  LogoutCurve as Logout,
  Setting3,
  Moon,
  GlobalEdit,
  ArrangeVerticalSquare,
  Danger,
  Ruler,
  CloseCircle,
  Message,
  Layer,
  Call,
  Flash,
  Signpost,
  Notification,
  Minus,
  UserSquare,
  Add,
  Graph,
  Calendar1 as CalendarIcon,
  CardTick,
  Camera,
  Apple,
  Map1,
  Box1,
  AddCircle,
  Eye,
  Edit2,
  Setting2,
  Location,
  Copy,
  DocumentCopy,
  SearchNormal1,
  Facebook,
  NoteRemove,
  Notepad2,
  Trash,
  Star1,
  Status,
  Clock,
  Printer,
  DocumentDownload,
  Share as ShareIcon,
  DocumentUpload,
  TickSquare,
  CloseSquare,
  MinusCirlce,
  Wallet,
  GlobalSearch,
  EmptyWallet,
  EmptyWalletAdd,
  MoneyRecive,
  WalletMinus,
  Refresh,
} from 'iconsax-react';
import React from 'react';
import Twitter from '@/components/icons/twitter';
import LinkedIn from '@/components/icons/linked-in';
import Instagram from '@/components/icons/instagram';
import Star from '@/components/icons/star';
import Group from '@/components/icons/group';
import SingleBed from '@/components/icons/single-bed';
import TwinBed from '@/components/icons/twin-bed';
import Chair from '@/components/icons/chair';
import Error from '@/components/icons/error';
import Success from '@/components/icons/success';
import Warning from '@/components/icons/warning';
import Box from '@/components/icons/box';
import Share from '@/components/icons/share';
import Bathroom from '@/components/icons/bathroom';
import Kitchen from '@/components/icons/kitchen';
import Swimming from '@/components/icons/swimming';
import Luxury from '@/components/icons/luxury';
import Tick from '@/components/icons/tick';
import SearchBar from '@/components/icons/search';
import HeartIcon from '@/components/icons/heart';
import Sort from '@/components/icons/sort';
import Filter from '@/components/icons/filter';
import List from '@/components/icons/list';
import StarOutline from '@/components/icons/star-outline';
import Guests from '@/components/icons/guests';
import House from '@/components/icons/house';
import SmallLogo from '@/components/icons/smallLogo';
import Bill from '@/components/icons/bill';
import NotificationStatus from '@/components/icons/notificationStatus';
import StatisticIcon from '@/components/icons/statisticIcon';
import Sync from '@/components/icons/sync_icon';
import Home from '@/components/icons/home';
import Security from '@/components/icons/Security';
import Efficiency from '@/components/icons/Efficiency';
import GreenCheck from '@/components/icons/greenCheck';
import Work from '@/components/icons/work';
import Send from '@/components/icons/send';
import XIcon from '@/components/icons/x';
import Check from '@/components/icons/Check';
import LocationMarker from '@/components/icons/locationMarker';
import UserIcon from './user-icon';
import FavoriteIcon from './favorite-icon';
import RentoorIcon from './rentor-icon';
import CalendarIconBlue from './calendar-icon-blue';
import MessagesIcon from './messages-icon';
import ArrowRight from './arrow-right';
import Property from './property';
import ArrowDown from './arrow-down';
import Cross from './cross';
import LocationIcon from './location-icon';
import DollarIcon from './dollar-icon';
import PropertyAmenities from './propertyAmenities';
import ArrowRightSmall from './arrow-right-small';
import CloseIcon from './close-icon';
import LanguageIcon from './language';
import QuestionsIcon from './questions';
import MessageIcon from './message-icon';
import LockIcon from './lock-icon';
import PolicyIcon from './policy-icon';
import ChevronLeft from './chevron-left';
import SuccessCircle from './successCircle';
import ColorSmallLogo from './color-small-logo';
import EmojiSmile from './emoji-smile';
import Image from './image';
import { UploadIcon } from './upload-icon';
import SearchGray from './search-gray';
import FilterGray from './filter-gray';
import SortGray from './sort-gray';
import ArrowDownSmall from './arrow-down-small';
import Download from './download';
import ShareIconSmall from './Share-icon-small';
import ShowIcon from './show-icon';
import SmallRightArrow from './small-right-arrow';
import SmallLeftArrow from './small-left-arrow';
import AccountIcon from './account-icon';
import CalendarIconSmall from './calendar-icon';
import Protection from './protection';
import BecomeHostIcon from './become-host-icon';
import AboutIcon from './about-icon';
import ColoredSmallLogo from './colored-small-logo';
import CameraIcon from './camera-icon';
import Resync from './resync';
import CancelSync from './CancelSync';
import SearchIcon from './search-icon';
import GuestIcon from './gust-icon';
import ErrorIcon from './error-icon';
import CopyIcon from './copy-icon';
import Saudi_Riyal_Symbol from './Saudi_Riyal_Symbol-2';
import CheckIcon from './check-icon';

export const icons = {
  LeftArrow: ArrowLeft2,
  RightArrow: ArrowRight2,
  ArrowUp: ArrowUp,
  ArrowUp2: ArrowUp2,
  ArrowDown: ArrowDown2,
  Calendar,
  Global,
  Send,
  UserSquare,
  List,
  Call,
  Filter,
  Camera,
  XIcon,
  Clock,
  Security,
  Efficiency,
  Home,
  Facebook,
  Work,
  Graph,
  Star1,
  StarOutline,
  Sort,
  SearchNormal1,
  Guests,
  Flash,
  Location,
  Heart,
  Apple,
  Moon,
  BurgerMenu,
  ProfileCircle,
  Sms,
  Twitter,
  LinkedIn,
  SearchBar,
  HeartIcon,
  Instagram,
  Notification,
  Star,
  Group,
  SearchNormal,
  Routing,
  SingleBed,
  TwinBed,
  Chair,
  Logout,
  Error,
  Success,
  Warning,
  Setting: Setting3,
  Arrange: ArrangeVerticalSquare,
  Ruler,
  Close: CloseCircle,
  Box,
  Share,
  Message,
  Layer,
  Signpost,
  Minus,
  GlobalSearch,
  Add,
  Bathroom,
  Kitchen,
  Saudi_Riyal_Symbol,
  Danger,
  Swimming,
  Luxury,
  Tick,
  CardTick,
  Map1,
  House,
  SmallLogo,
  NotificationStatus,
  Bill,
  Box1,
  StatisticIcon,
  AddCircle,
  Eye,
  Edit2,
  Setting2,
  Copy,
  DocumentCopy,
  NoteRemove,
  Notepad2,
  Trash,
  Sync,
  Status,
  GreenCheck,
  Printer,
  DocumentDownload,
  ShareIcon,
  DocumentUpload,
  TickSquare,
  CloseSquare,
  Check,
  MinusCirlce,
  LocationMarker,
  Cross,
  UserIcon,
  FavoriteIcon,
  RentoorIcon,
  CalendarIconBlue,
  CalendarIcon,
  GlobalEdit,
  MessagesIcon,
  ArrowRight,
  Property,
  ArrowDown2: ArrowDown,
  LocationIcon,
  DollarIcon,
  PropertyAmenities,
  CloseIcon,
  ArrowRightSmall,
  Wallet,
  EmptyWallet,
  EmptyWalletAdd,
  WalletMinus,
  MoneyRecive,
  LanguageIcon,
  QuestionsIcon,
  MessageIcon,
  LockIcon,
  PolicyIcon,
  ChevronLeft,
  SuccessCircle,
  ColorSmallLogo,
  EmojiSmile,
  Image,
  UploadIcon,
  FilterGray,
  SearchGray,
  SortGray,
  ArrowDownSmall,
  Download,
  ShareIconSmall,
  ShowIcon,
  SmallLeftArrow,
  SmallRightArrow,
  AccountIcon,
  CalendarIconSmall,
  Protection,
  BecomeHostIcon,
  AboutIcon,
  ColoredSmallLogo,
  CameraIcon,
  Refresh,
  Resync,
  CancelSync,
  SearchIcon,
  GuestIcon,
  ErrorIcon,
  CopyIcon,
  CheckIcon,
} as const;

export type IconWrapperProps = {
  name: keyof typeof icons;
} & IconProps;

const IconWrapper: FC<IconWrapperProps> = ({ name, ...props }): ReactElement => {
  const Icon = icons[name];

  return <Icon {...props} />;
};

export default IconWrapper;
