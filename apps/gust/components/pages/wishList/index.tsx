'use client';
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ArrowButton from '@/components/common/arrow-button';
import Breadcrumbs from '@/components/common/breadcrumbs';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { BreadCrumbItem } from '@/types/common';
import { useGetWishlistCities } from '@/queries/wisthlist';
import { CityList } from './cityList';
import { useGetUserProfile } from '@/queries/authentication';
import RequireLoginFullPage from '@/components/authentication/require-login-full-page';
import { ROUTES } from '@/constants';

export default function WishList() {
  const ct = useScopedI18n('common');
  const t = useScopedI18n('favorite');
  const router = useRouter();
  const { data: wishlist, isPending, refetch, isRefetching } = useGetWishlistCities();
  const { isPending: isGetProfilePending, data: profileData } = useGetUserProfile();
  const items: BreadCrumbItem[] = [{ href: '/', label: ct('home') }, { label: t('favorite') }];
  const handleBackHomeClick = (): void => {
    router.push(ROUTES.HOME);
  };

  useEffect(() => {
    refetch();
  }, []);

  return (
    <>
      <div className="mt-8">
        <div className="mb-6 hidden flex-col-reverse items-stretch justify-between gap-y-4 md:flex md:flex-row md:items-center">
          <Breadcrumbs items={items} />
          <ArrowButton label={ct('back_home')} onClick={handleBackHomeClick} />
        </div>
        <h4 className="text-secondary mobile:block mb-6 hidden text-3xl font-bold">{t('favorite')}</h4>
      </div>

      <>
        {!profileData && !isGetProfilePending ? (
          <div className="mobile:mb-12 mb-4 mt-4">
            <RequireLoginFullPage description={t('loginMessage')} />
          </div>
        ) : (
          <CityList wishList={wishlist} isPending={isPending} isRefetching={isRefetching} />
        )}
      </>
    </>
  );
}
