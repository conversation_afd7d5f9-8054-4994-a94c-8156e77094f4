'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import ArrowButton from '@/components/common/arrow-button';
import Breadcrumbs from '@/components/common/breadcrumbs';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { BreadCrumbItem } from '@/types/common';
import { getLayoutDirection } from '@/lib/utils';
import NoPropertiesFound from '@/components/common/NoPropertiesFound';
import ClassificationCard from '@/components/homepage/propertis/classification-card';
import PropertySortDropdown from '@/components/homepage/propery-filter/property-sort-dropdown';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import { Separator } from '@/components/ui/separator';
import Location from '@/components/location/location-map';
import { Classification, HomeGustPage } from '@/types/homepage';
import ClassificationCardSkeleton from '@/components/homepage/propertis/classification-card-skeleton';
import PropertyFilter from '@/components/homepage/propery-filter/property-filter';
import ShowMapBtn from '@/components/location/show-map-btn';
import ChipCarousel from '@/components/homepage/chip-carousel';
import { SplideSlide } from '@splidejs/react-splide';
import PropertySortModal from '@/components/homepage/propery-filter/property-sort-dialog';
import FilterBtn from '@/components/homepage/propery-filter/filter-btn';
import { ROUTES } from '@/constants';
import LocationBottomSheet from '@/components/location/location-bottom-sheet';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import useGetPaginatedData from '@/hooks/use-get-paginated-data';
import { useGetPropertySearch } from '@/queries/homepage';
import styles from './styles.module.scss';

type CityWishListProps = {
  cityId: string;
};

export default function CityWishList({ cityId }: CityWishListProps) {
  const ct = useScopedI18n('common');
  const tp = useScopedI18n('property_search');

  const t = useScopedI18n('header');
  const [isMapView, setIsMapView] = useState<boolean>(false); // State to track map view
  const router = useRouter();
  const locale = useCurrentLocale();
  const dir = getLayoutDirection(locale);
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false); // Track drawer open state
  const propertySearch = useGetPropertySearch();
  const itemsProperty = propertySearch.data?.data ?? null;
  const { finalListItems, isMobile, Pagination, loadingLength, loading, setFinalListItems } = useGetPaginatedData<
    HomeGustPage,
    Classification
  >({
    mutationFn: propertySearch,
    dataGetter: (apiData) => {
      return apiData?.properties ?? [];
    },
    initialQuery: { wishlists: '1', has_statistics: '1', city_feild_id: cityId ? `${cityId}` : '' },
  });

  const items: BreadCrumbItem[] = [
    { href: '/', label: ct('home') },
    { label: t('favorite'), href: ROUTES.WISHLIST },
    { label: itemsProperty?.data?.properties[0]?.city_name || '...' },
  ];

  // Handle remove from wishlist
  const handleRemoveFromWishlist = (propertyId: number): void => {
    setFinalListItems((prevData) => prevData.filter((property) => property.id !== propertyId));
  };

  const handleBackHomeClick = (): void => {
    router.push(ROUTES.WISHLIST);
  };

  // Function to toggle map view
  const showMapView = () => {
    setIsMapView(true); // Toggle the state of map view
  };
  const hideMapView = () => {
    setIsMapView(false);
  };

  return (
    <>
      <div className="mobile:hidden">
        <div className="bg-white-full border-gray-75 fixed  left-0 top-0  z-50 w-full border-b">
          <div className="checkout-container-layout">
            {propertySearch.isPending || !itemsProperty ? (
              <div className="flex h-16 animate-pulse items-center gap-1">
                <button
                  title='Back'
                  className="bg-transparent p-1">
                  <Skeleton className="h-5 w-5 rounded bg-gray-200" />
                </button>
                <div className="bg-gray-250 border-gray-150 flex h-10 w-full items-center gap-2 rounded-md border px-3 py-2">
                  <Skeleton className="h-4 w-16 rounded bg-gray-200" />
                  <Skeleton className="ms-2 h-4 w-16 rounded bg-gray-200" />
                </div>
              </div>
            ) : (
              <div className="flex h-[60px] items-center  gap-1 ">
                <Link href={ROUTES.WISHLIST} className="bg-transparent p-1">
                  <IconWrapper name={dir === 'rtl' ? 'RightArrow' : 'LeftArrow'} className="text-secondary" />
                </Link>
                <div
                  // onClick={handleSearchClick}
                  className="bg-gray-250 hover:border-primary-200 focus-visible:border-primary-200 md-text border-gray-150 flex h-10 w-full cursor-pointer items-center gap-2 truncate overflow-ellipsis rounded-md border px-3 py-2 placeholder:text-gray-100 focus-visible:outline-none disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-100 disabled:opacity-50">
                  <span className="text-md text-secondary font-bold">{finalListItems?.[0]?.city_name}</span>
                  <span className="text-md text-primary truncate">
                    {`(${itemsProperty?.pages?.total_items ?? 0} ${tp('property')})`}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="border-gray-75 border-b py-2.5">
          <div className="checkout-container-layout flex flex-1  flex-col ">
            {propertySearch.isPending || !itemsProperty ? (
              <div className="flex gap-2">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Skeleton className="h-9 flex-1  rounded-lg bg-gray-100" />
                ))}
              </div>
            ) : (
              <div className={styles.cityWishList}>
                <ChipCarousel>
                  <SplideSlide>
                    <FilterBtn
                      className="flex-1 px-5"
                      onClick={showMapView}
                      icon={<IconWrapper name="Map1" size={18} />}
                      title={t('map')}
                    />
                  </SplideSlide>
                  <SplideSlide>
                    <Separator orientation="vertical" />
                  </SplideSlide>
                  <SplideSlide>
                    <PropertyFilter
                      cityId={cityId}
                      setIsDrawerOpen={setIsDrawerOpen}
                      isDrawerOpen={isDrawerOpen}
                      itemsProperty={itemsProperty}
                    />
                  </SplideSlide>

                  <SplideSlide>
                    <PropertySortModal />
                  </SplideSlide>
                </ChipCarousel>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="checkout-container-layout mt-4 flex flex-1 flex-col">
        <div className="hidden flex-col-reverse items-stretch justify-between gap-y-4 md:flex md:flex-row md:items-center">
          <Breadcrumbs items={items} />

          <div className="flex items-center gap-4">
            <PropertySortDropdown />
            <PropertyFilter
              cityId={cityId}
              setIsDrawerOpen={setIsDrawerOpen}
              isDrawerOpen={isDrawerOpen}
              itemsProperty={itemsProperty}
            />

            <Separator orientation="vertical" />
            <ArrowButton size="lg" label={ct('back')} onClick={handleBackHomeClick} />
          </div>
        </div>

        {loading && !isMapView ? (
          <div className="mobile:grid-cols-2 mobile:my-6 mb-8 grid  grid-cols-1   gap-4 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
            {Array.from({ length: loadingLength }).map((_, index) => (
              <ClassificationCardSkeleton />
            ))}
          </div>
        ) : (
          <>
            {!isMapView && (
              <>
                {finalListItems?.length > 0 ? (
                  <>
                    <div
                      className={
                        'mobile:grid-cols-2 mobile:my-6 mb-6 mt-0  grid  grid-cols-1   gap-4 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'
                      }>
                      {finalListItems.map((property) => (
                        <ClassificationCard
                          key={property.id}
                          classification={property}
                          cityId={cityId}
                          onRemove={() => handleRemoveFromWishlist(property.id)} // Pass the remove handler
                        />
                      ))}
                    </div>
                    {!isMobile && <ShowMapBtn onClick={showMapView} />}
                    <div className="mobile:mb-8">
                      <Pagination className="mt-0" />
                    </div>
                  </>
                ) : (
                  <div className="align-center flex flex-1 flex-col justify-center">
                    <NoPropertiesFound />
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>

      {isMapView && (
        <>
          {isMobile ? (
            <LocationBottomSheet
              isOpen={isMapView}
              isGettingData={propertySearch.isPending}
              setIsDrawerOpen={setIsMapView}
              properties={itemsProperty?.data?.properties ?? []}
            />
          ) : (
            <div className="relative grid h-[80vh] w-full  xl:grid-cols-1 ">
              <div className="z-100 fixed bottom-5 left-1/2  flex w-[150px] -translate-x-1/2 transform  justify-center">
                <Button
                  onClick={hideMapView}
                  className="sm-text text-l-20 bg-primary hover:bg-primary-200 h-auto gap-1 rounded-full border p-3 py-3 text-white lg:mt-0">
                  <IconWrapper name="List" size={18} />
                  <p className="lg-text">{tp('showList')}</p>
                </Button>
              </div>

              <Location isLoading={propertySearch.isPending} properties={itemsProperty?.data?.properties ?? []} />
            </div>
          )}
        </>
      )}
    </>
  );
}
