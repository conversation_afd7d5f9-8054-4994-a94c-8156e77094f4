import React, { FC, ReactElement, useEffect, useState } from 'react';
import TimerComponent from '@/components/common/timer';
import IconWrapper from '@/components/icons';
import { Separator } from '@/components/ui/separator';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { cn, formatDateRange, formatDateTime } from '@/lib/utils';
import { ReservationDetailsData, ReservationStatus } from '@/types/reservation';
import Link from 'next/link';
import ActionsBtns from './actions-btns';
import ReservationDetailsSkeleton from './reservation-details-skeleton';
import ReservationDetailsCard from './reservation-details-card';
import { ReservationPrice, ReservationTotalAmount } from './utils';
import { ROUTES_WITH_PARAMS } from '@/constants';
import { Accordion, AccordionContent, AccordionItem } from '@/components/ui/accordion';
import { AccordionTrigger } from '@radix-ui/react-accordion';

interface ReservationDetailsProps {
  isPending: boolean;
  reservationDetails: ReservationDetailsData;
  isSelected: number | null;
  currentTab: string;
  inDialog?: boolean;
}

const ReservationDetails: FC<ReservationDetailsProps> = ({
  isPending,
  reservationDetails,
  isSelected,
  currentTab,
  inDialog = false,
}): ReactElement => {
  const [loading, setLoading] = useState(true); // State to track loading status
  // const currT = useScopedI18n('currency');
  const reT = useScopedI18n('reservation_details');
  const sT = useScopedI18n('status');

  useEffect(() => {
    // Simulate data fetching delay
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000); // Adjust time for your loading behavior
    return () => clearTimeout(timer);
  }, [reservationDetails, isSelected]);

  const status = reservationDetails?.status as ReservationStatus | undefined;

  if (loading || isPending) {
    return <ReservationDetailsSkeleton inDialog={inDialog} withHeader={!inDialog} />;
  }

  return (
    <div
      className={`overflow-hidden ${
        !inDialog
          ? cn(
              ' bg-white-50 sticky top-24 flex w-full flex-col rounded-lg shadow-lg',
              isSelected ? 'visible' : 'invisible'
            )
          : ''
      }`}>
      <>
        {inDialog ? (
          <div className="mt-4 px-4">
            <ReservationDetailsCard
              showTimeRemaining={reservationDetails?.booking_type !== 'instant_book' && currentTab !== 'finished'}
              reservationDetails={reservationDetails}
            />
            <Separator className="mt-3" orientation="horizontal" />
          </div>
        ) : (
          <>
            <h2 className="text-secondary container flex items-center px-5 py-4  text-2xl font-bold">{reT('title')}</h2>
            <Separator orientation="horizontal" />
            <div className="border-gray/20  border-b p-4">
              <h3 className="text-secondary text-md mb-2 font-bold">{`${reservationDetails?.classification_name}`}</h3>
              <div className="flex items-start justify-between gap-4 ">
                <div className="flex flex-col gap-2">
                  <p className="text-sm text-gray-400">
                    {reT('booking_status')} :{' '}
                    <span className="text-sm text-gray-400">{status ? sT(status) : sT('Pending')}</span>
                  </p>
                  <p className="text-sm text-gray-400">
                    {reT('booking_number')}: {reservationDetails?.id}
                  </p>
                </div>
                {reservationDetails?.status === 'Pending' || reservationDetails?.status === 'Pre-Accepted' ? (
                  <>
                    <Separator orientation="vertical" className="self-start" />
                    {/* Time remaining */}
                    <div className="text-sm text-gray-400">
                      <p>
                        {reT('remaining_time')}:{' '}
                        <span className="text-primary font-bold">
                          <TimerComponent targetDate={reservationDetails?.expiration_date || ''} />
                        </span>
                      </p>
                    </div>
                  </>
                ) : (
                  ''
                )}
              </div>

              {['Cancelled', 'Declined'].includes(reservationDetails?.status) ? (
                <Accordion type="multiple">
                  <AccordionItem value="test" key="test" className="shadow-none">
                    <AccordionTrigger className="text-primary mt-3 flex w-full items-center justify-between text-sm shadow-none">
                      <span>
                        {reservationDetails?.status === 'Declined' ? reT('declineReason') : reT('cancelReason')}
                      </span>
                      <IconWrapper name="ArrowDownSmall" />
                    </AccordionTrigger>
                    <AccordionContent className="m-0 p-3">
                      <p className="text-secondary mb-2 text-sm">{reservationDetails.cancelled_reason}</p>
                      <p className="text-sm text-gray-400">
                        {reT('booking_cancelled')} {reservationDetails?.cancelled_messages}
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              ) : (
                <>
                  {reservationDetails?.status !== 'Expired' && (
                    <div className="py-3 pb-0">
                      <ActionsBtns
                        wp_status={reservationDetails?.status as ReservationStatus}
                        reservationDetails={reservationDetails}
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          </>
        )}
      </>

      <div className="scrollable-content max-h-[50vh] overflow-auto p-4">
        {/* Booking Title */}

        <div className="space-y-4">
          <div className="flex  justify-between gap-2">
            <h3 className="text-secondary flex-1 text-xl font-bold">{`${reservationDetails?.classification_name}`}</h3>
            <Link
              className="text-primary block  text-nowrap text-sm"
              href={{
                pathname: ROUTES_WITH_PARAMS.ROOM_DETAILS(reservationDetails?.id),
                query: {
                  pageName: 'reservation',
                  room_classification_id: reservationDetails?.room_classification_id,
                },
              }}
              target="_blank"
            >
              {reT('property_details')}
            </Link>
          </div>

          <div className="flex items-start gap-2">
            <IconWrapper name="Location" className="text-secondary" size={20} />

            <div className="flex flex-1 flex-col gap-1">
              <h3 className="text-secondary text-md flex items-center justify-between font-bold">
                {reT('location.title')}
              </h3>
              {(reservationDetails?.city_name || reservationDetails?.neighborhood_name) && (
                <p className="text-sm text-gray-600">
                  {reservationDetails?.city_name}, {reservationDetails?.neighborhood_name}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-start gap-2">
            <IconWrapper name="CalendarIcon" className="text-secondary" />
            <div className="flex flex-col gap-1">
              <p className="text-secondary text-md font-bold">
                {formatDateRange(reservationDetails?.checkin_date, reservationDetails?.checkout_date)}
              </p>
              <p className="text-md text-gray-600">
                {reT('dates.checkin_time')} :
                {formatDateTime(reservationDetails?.checkin_date, reservationDetails?.checkin_time)}
              </p>
              <p className="text-md text-gray-600">
                {reT('dates.checkout_time')} :
                {formatDateTime(reservationDetails?.checkout_date, reservationDetails?.checkout_time)}
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2">
            <IconWrapper name="Guests" className="text-secondary" />
            <div className="flex flex-col gap-1">
              <p className="text-secondary text-md font-bold">{reT('guests_count.title')}</p>
              <p className="text-md text-gray-600">{reservationDetails?.number_of_guests}</p>
            </div>
          </div>

          <div className="flex items-start gap-2">
            <IconWrapper name="Moon" className="text-secondary" size={20} />
            <div className="flex flex-col gap-1">
              <p className="text-secondary text-md font-bold">{reT('nights_count.title')}</p>
              <p className="text-md text-gray-600">{reservationDetails?.total_nights}</p>
            </div>
          </div>

          <Separator orientation="horizontal" />
          <div className="flex flex-col items-start gap-2 ">
            <p className="text-secondary text-md font-bold">{reT('cancellation_policy.title')}</p>
            <p className="text-md text-gray-600">{reservationDetails?.cancellation?.policy}</p>
          </div>
          <Separator orientation="horizontal" />
          <ReservationPrice reservationDetails={reservationDetails} />
        </div>
        <div>
          <ReservationTotalAmount reservationDetails={reservationDetails} />
        </div>
      </div>
    </div>
  );
};

export default ReservationDetails;
