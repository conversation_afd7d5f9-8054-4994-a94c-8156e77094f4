'use client';
import { useCallback, useState, type FC, type ReactElement } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { Button } from '@/components/ui/button';
import { useCancelGustReservation, useGetCancellationReasons, useGetReservationDetails } from '@/queries/reservation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useValidateReservationSchema } from '@/validations/reservation';
import { getMessagesFromResponse } from '@/lib/utils';
import { toast } from 'sonner';
import { serializeQueryParams, useAddQueryParams, usePropertySearchParams } from '@/hooks/use-query-params';
import { usePathname, useRouter } from 'next/navigation';
import ReservationCancel from '../reservation-cancel';
import { Drawer, DrawerClose, DrawerContent, DrawerFooter, DrawerHeader, DrawerTitle } from '@/components/ui/drawer';
import IconWrapper from '@/components/icons';
import { Separator } from '@/components/ui/separator';
import { cancellationReasons } from '@/types/reservation';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';
interface CancelReservationModalProps {
  id: string;
  open: boolean;
  setOpen: (value: boolean) => void;
  isBottomSheet: boolean;
  status?: string;
}

const CancelReservationModal: FC<CancelReservationModalProps> = ({
  isBottomSheet,
  id,
  setOpen,
  status,
  open,
}): ReactElement => {
  const t = useScopedI18n('common');
  const cnt = useScopedI18n('cancelReservation');
  const { query, setNewQuery } = usePropertySearchParams();
  const reservationSchema = useValidateReservationSchema();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const cancelReservation = useCancelGustReservation(id);
  const { data: cancellationReasons, isPending: cancelPending } = useGetCancellationReasons('Guest');
  console.log({ cancellationReasons });
  const createQueryParams = useAddQueryParams();
  const pathname = usePathname();
  const router = useRouter();
  const { sendGAEvent } = useGAEvent();

  const form = useForm<z.infer<typeof reservationSchema>>({
    resolver: zodResolver(reservationSchema),
    defaultValues: {
      cancel_reason: '',
      cancel_message: '',
    },
  });

  const { refetch } = useGetReservationDetails(id ? `${id}` : '');

  const handleSubmitSearch = (): void => {
    setNewQuery('finished', 'type');
    const queryValues = serializeQueryParams(query);
    router.replace(`${pathname}?${createQueryParams(queryValues)}&type=finished`);
  };
  // Function to update the query
  const handleCancelReservationSubmit = () => setIsConfirmDialogOpen(true);

  const handleConfirmReservationSubmit = useCallback(() => {
    sendGAEvent({ action: 'reservation_cancelled' });
    const values = form.watch();
    cancelReservation.mutate(values, {
      onSuccess: () => {
        setOpen(false);
        setIsConfirmDialogOpen(false);
        refetch();
        handleSubmitSearch();
      },
      onError: (e) => {
        const errors = getMessagesFromResponse(e);
        if (!Array.isArray(errors)) {
          toast.error(errors);
        } else {
          errors.forEach((item) => {
            toast.error(Array.isArray(item[1]) ? item[1][0] : item[1]);
          });
        }
      },
    });
  }, []);

  const cancellationReasonsData = cancellationReasons?.data.data;
  const isLoading = cancelPending;
  const handleClose = () => {
    setOpen(false);
  };
  const handleCloseConfirmDialog = () => setIsConfirmDialogOpen(false);

  return (
    <>
      {isBottomSheet ? (
        <Drawer onOpenChange={setOpen} open={open && !isConfirmDialogOpen} direction="bottom">
          <DrawerContent isBottom>
            <DrawerHeader className="text-secondary px-6 py-3 font-bold">
              <DrawerTitle> {cnt('title')}</DrawerTitle>
              <DrawerClose onClick={() => setOpen(false)} asChild>
                <Button variant="ghost" className="hover:text-secondary-400 p-0">
                  <IconWrapper name="Close" className="text-secondary" size={28} />
                </Button>
              </DrawerClose>
            </DrawerHeader>
            <Separator orientation="horizontal" />

            <ReservationCancel
              className="py-4"
              cancellationReasonsData={cancellationReasonsData as cancellationReasons[]}
              isPending={isLoading}
              form={form}
              handleCancelReservationSubmit={handleCancelReservationSubmit}
            />
            <DrawerFooter isBottom>
              <div className="flex justify-center gap-5 ">
                <Button onClick={handleClose} className="hover:bg-primary-300 h-12  w-full">
                  {t('cancel')}
                </Button>
                <Button
                  onClick={form.handleSubmit(handleCancelReservationSubmit)}
                  className="bg-gray h-12 w-full hover:bg-gray-400"
                  type="submit"
                  disabled={!form.formState.isValid}>
                  {t('confirm')}
                </Button>
              </div>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      ) : (
        <Dialog open={open && !isConfirmDialogOpen} onOpenChange={setOpen}>
          <DialogContent className="max-w-sm md:max-w-[405px]">
            <DialogHeader>
              <DialogTitle>{cnt('title')}</DialogTitle> {/* Translated text */}
              <DialogClose onClick={handleClose} />
            </DialogHeader>
            <ReservationCancel
              cancellationReasonsData={cancellationReasonsData as cancellationReasons[]}
              isPending={isLoading}
              form={form}
              handleCancelReservationSubmit={handleCancelReservationSubmit}
            />
            <div className=" flex justify-center  gap-5 p-4">
              <Button onClick={handleClose} className="bg-gray  h-12 w-full  hover:bg-gray-400">
                {t('cancel')}
              </Button>
              <Button
                onClick={form.handleSubmit(handleCancelReservationSubmit)}
                className=" h-12  w-full "
                type="submit"
                disabled={!form.formState.isValid}>
                {t('confirm')}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent className="max-w-sm md:max-w-[405px]">
          <DialogHeader>
            <DialogTitle>{cnt('confirmTitle')}</DialogTitle>
          </DialogHeader>
          <div className="flex max-h-[53vh] min-h-[100px] flex-col gap-4 overflow-auto px-5">
            <p className="text-md text-gray-400">{cnt('confirmMessage')}</p>
            <div className="flex justify-center gap-5 p-4">
              <Button
                className="bg-primary hover:bg-primary-400 h-12 w-full "
                disabled={cancelReservation.isPending}
                loading={cancelReservation.isPending}
                onClick={handleConfirmReservationSubmit}>
                {t('confirm')}
              </Button>
              <Button
                type="button"
                className="bg-gray h-12  w-full hover:bg-gray-400"
                onClick={handleCloseConfirmDialog}>
                {t('cancel')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CancelReservationModal;
