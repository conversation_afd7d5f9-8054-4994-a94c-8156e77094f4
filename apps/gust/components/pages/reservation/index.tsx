'use client';

import { FC, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import ArrowButton from '@/components/common/arrow-button';
import Breadcrumbs from '@/components/common/breadcrumbs';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { BreadCrumbItem } from '@/types/common';
import TabListTriggers from '@/components/common/tab-list-triggers';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useGetReservation, useGetReservationDetails } from '@/queries/reservation';
import { usePropertySearchParams } from '@/hooks/use-query-params';
import ReservationDetails from './reservationDetails';
import { ReservationSkeletonPage } from './reservation-skeleton';
import ReservationSortDropdown from './reservation-sort-dropdown';
import { Separator } from '@/components/ui/separator';
import { useGetResevationTabs } from '@/hooks/use-reservation-static-data';
import ReservationList from './reservation-list';
import IconWrapper from '@/components/icons';
import { InvoiceDetailsItem, ReservationDetailsData, ReservationType, reservationTypeItem } from '@/types/reservation';
import useWindowSizeQuery from '@/hooks/use-window-size-query';
import { useModal } from '@/hooks/use-modal-status';
import ReservationDetailsDialog from './modals/reservation-details-dialog';
import { ReservationContext } from './reservation-context';
import CancelReservationModal from './modals/cancel-reservation-modal';
import RatingReservationModal from './modals/rating-reservation-modal';
import ReservationEditDrawer from './bottom-sheets/reservation-edit-drawer';
import ReservationAgainDrawer from './bottom-sheets/reservation-again-drawer';
import useGetPaginatedData from '@/hooks/use-get-paginated-data';
import ReservationSearchAndFilter from './reservation-search-and-filter';
import FilterBtn from '@/components/homepage/propery-filter/filter-btn';
import FilterReservationModal from './modals/filter-reservation-modal';
import InvoiceModal from './modals/invoice-details-modal';
import InvoicesListDialog from './modals/invoices-list-dialog';

const Reservation: FC = () => {
  const router = useRouter();
  const ct = useScopedI18n('common');
  const t = useScopedI18n('header');
  const tr = useScopedI18n('my_reservations');
  const { query, setNewQuery, submitSearch, resetQueryByKeys } = usePropertySearchParams();
  const reserveTabs = useGetResevationTabs();
  const searchParams = useSearchParams();
  const reservation_id = Number(searchParams.get('reservation_id')) || null;
  const reservation = useGetReservation();
  const [selectedProperty, setSelectedProperty] = useState<number | null>(null);
  const { data, isPending, refetch } = useGetReservationDetails(selectedProperty ? `${selectedProperty}` : '');
  const isLG = useWindowSizeQuery('lg');
  const isMD = useWindowSizeQuery('md');
  const isMobile = useWindowSizeQuery('mobile');

  const { isOpen: isDetailsModalOpen, setIsOpen: setIsDetailsModalOpen } = useModal(false);
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);
  const [isEditReservationModalOpen, setIsEditReservationModalOpen] = useState(false);
  const [isReserveAgainDrawerOpen, setIsReserveAgainDrawerOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceDetailsItem>();
  const [isRatingModalOpen, setIsRatingModalOpen] = useState(false);
  const [isInvoicesListModalOpen, setIsInvoicesListModalOpen] = useState(false);
  const [editReservationDetailsOpen, setIsEditReservationDetailsOpen] = useState(false);
  const [isEditPending, setIsEditPending] = useState(false);

  const items: BreadCrumbItem[] = [{ href: '/', label: ct('home') }, { label: t('reservation') }];

  const { finalListItems, loadingLength, Pagination, firstLoad, loading, resetChangeParamLoading } =
    useGetPaginatedData<ReservationType, reservationTypeItem>({
      mutationFn: reservation,
      dataGetter: (apiData) => {
        return apiData?.reservation ?? [];
      },
      pageSize: 6,
      initialQuery: !query.type ? { type: 'now' } : {},
      onPageChange: () => setSelectedProperty(reservation_id),
    });

  const handleCancelBtnClick = () => {
    setIsCancelModalOpen(true);
  };
  const handleEditReservationClick = () => {
    setIsEditReservationModalOpen(true);
  };
  const handleReservationAgainClick = () => {
    setIsReserveAgainDrawerOpen(true);
  };
  const handleRatingClick = () => {
    setIsRatingModalOpen(true);
  };

  const currentTab = query.type;

  const handleBackHomeClick = (): void => router.push(`/`);
  const reservationFilter = reservation.data?.data.data?.reservation_filtering;
  const reservationDetails = data?.data.data;

  useEffect(() => {
    if (finalListItems && !selectedProperty) {
      if (!isLG) {
        if (reservation_id) {
          setSelectedProperty(reservation_id); // Set first reservation as selected if no ID is selected yet
        } else {
          setSelectedProperty(finalListItems?.[0]?.id); // Set first reservation as selected if no ID is selected yet
        }
      }
    }
  }, [finalListItems, isLG]);
  useEffect(() => {
    refetch();
  }, []);

  // Show skeleton only during initial load
  if (firstLoad) {
    return <ReservationSkeletonPage />;
  }
  const isHaveData = !finalListItems?.length && !reservation.isPending;

  const handleSelectProperty = (propertyId: number) => {
    setSelectedProperty(propertyId);

    if (isLG) {
      setIsDetailsModalOpen(true);
    }
  };

  const handleSearchSubmit = (searchValue: string) => {
    setNewQuery(searchValue, 'property_name', submitSearch);
  };

  const handleViewInvoiceClick = () => {
    const { room_classification_id, main_id, units } = reservationDetails || {};
    const unitNumber = units?.[0]?.unit_number;

    const queryParams = new URLSearchParams({
      id: room_classification_id?.toString() || '',
      chat_id: main_id?.toString() || '',
      invoice: 'invoice',
    });

    if (unitNumber) {
      queryParams.set('unit_number', unitNumber);
    }

    router.push(`/chat?${queryParams.toString()}`);
  };

  const handleViewInvoiceItemClick = (invoiceData: InvoiceDetailsItem) => {
    setSelectedInvoice(invoiceData);
    setIsInvoiceModalOpen(true);
  };

  const handleSelectTab = (value: string) => {
    resetQueryByKeys(['order_by', 'main_id', 'checkin', 'checkout', 'status'], undefined, () => {
      resetChangeParamLoading();
      setNewQuery(value, 'type', submitSearch);
      setSelectedProperty(null);
    });
  };

  return (
    <div className="flex flex-1 flex-col gap-y-8 pt-8">
      <div className="hidden flex-col-reverse items-stretch justify-between gap-y-4 md:flex md:flex-row md:items-center">
        <Breadcrumbs items={items} />
        <div className="flex items-center gap-4">
          <ReservationSortDropdown />
          <FilterBtn
            onClick={() => setIsFilterDialogOpen(true)}
            title={t('search.filter')}
            icon={<IconWrapper className="text-gray" name="Filter" size={20} />}
          />
          <Separator orientation="vertical" />
          <ArrowButton label={ct('back_home')} onClick={handleBackHomeClick} />
        </div>
      </div>

      <Tabs value={query.type} className="flex flex-1 flex-col" defaultValue="now" orientation="vertical">
        <div className="flex flex-1 flex-col gap-4 md:grid md:grid-cols-12">
          <div className="col-span-12 md:col-span-3  lg:col-span-2">
            <TabListTriggers
              items={reserveTabs}
              vertical={!isMD}
              handleSelectType={handleSelectTab}
              containerClassName="mb-0"
              className="w-full"
            />
            <ReservationSearchAndFilter
              setIsFilterDialogOpen={setIsFilterDialogOpen}
              reservationFilter={reservationFilter}
              isMobile={isMobile}
              handleSearchSubmit={handleSearchSubmit}
              searchQueryValue={query.property_name}
            />
          </div>
          <div
            className={`flex flex-1 flex-col ${!isHaveData ? 'col-span-12 md:col-span-9 lg:col-span-6' : 'col-span-12 md:col-span-9 lg:col-span-10'}`}>
            <TabsContent className="flex  flex-col bg-transparent" value="now">
              <ReservationList
                loading={loading}
                pagination={<Pagination className="mb-0" />}
                notFoundMessage={tr('notFoundCurrent')}
                reservationDate={finalListItems}
                reservation={reservation}
                isSelected={selectedProperty}
                setIsSelected={handleSelectProperty}
                loadingLength={loadingLength}
              />
            </TabsContent>

            <TabsContent className=" flex  flex-col bg-transparent" value="finished">
              <ReservationList
                loading={loading}
                pagination={<Pagination className="mb-0" />}
                notFoundMessage={tr('notFoundCurrent')}
                reservationDate={finalListItems}
                reservation={reservation}
                isSelected={selectedProperty}
                setIsSelected={handleSelectProperty}
                loadingLength={loadingLength}
              />
            </TabsContent>
          </div>
          <ReservationContext.Provider
            value={{
              handleCancelBtnClick,
              handleEditReservationClick,
              handleReservationAgainClick,
              handleRatingClick,
              isEditPending,
              setIsEditPending,
              handleViewInvoiceClick,
            }}>
            {!isLG && (
              <div className={`${!isHaveData ? 'lg:col-span-4' : 'col-span-0 hidden'}`}>
                {finalListItems?.length && !reservation.isPending ? (
                  <ReservationDetails
                    isSelected={selectedProperty}
                    reservationDetails={reservationDetails as ReservationDetailsData}
                    isPending={isPending}
                    currentTab={currentTab as string}
                  />
                ) : null}
              </div>
            )}
            {isLG &&
              isDetailsModalOpen &&
              !isEditReservationModalOpen &&
              !isRatingModalOpen &&
              !isCancelModalOpen &&
              !editReservationDetailsOpen &&
              selectedProperty &&
              !isInvoiceModalOpen &&
              !isInvoicesListModalOpen && (
                <ReservationDetailsDialog
                  isBottomSheet={isMobile}
                  isOpen={isDetailsModalOpen}
                  setIsOpen={setIsDetailsModalOpen}
                  selectedProperty={selectedProperty}
                  reservationDetails={reservationDetails as ReservationDetailsData}
                  isPending={isPending}
                  currentTab={currentTab as string}
                />
              )}
          </ReservationContext.Provider>
          {selectedProperty && reservationDetails && (
            <>
              {isEditReservationModalOpen && (
                <ReservationEditDrawer
                  editReservationDetailsOpen={editReservationDetailsOpen}
                  setIsEditReservationDetailsOpen={setIsEditReservationDetailsOpen}
                  isBottomSheet={isMobile}
                  setIsPending={setIsEditPending}
                  setIsOpen={setIsEditReservationModalOpen}
                  open={isEditReservationModalOpen}
                  reservationDetails={reservationDetails}
                  id={`${reservationDetails?.room_classification_id}`}
                  isAcceptEdit={reservationDetails.status === 'Accepted'}
                  isEdit
                />
              )}
              {isReserveAgainDrawerOpen && (
                <ReservationAgainDrawer
                  isBottomSheet={isMobile}
                  open={isReserveAgainDrawerOpen}
                  setOpen={setIsReserveAgainDrawerOpen}
                  id={`${reservationDetails?.room_classification_id}`}
                  reservationDetails={reservationDetails}
                />
              )}
              {isCancelModalOpen && (
                <CancelReservationModal
                  status={reservationDetails?.status}
                  isBottomSheet={isMobile}
                  open={isCancelModalOpen}
                  setOpen={setIsCancelModalOpen}
                  id={`${reservationDetails?.id}`}
                />
              )}
              {isRatingModalOpen && (
                <RatingReservationModal
                  isBottomSheet={isMobile}
                  open={isRatingModalOpen}
                  setOpen={setIsRatingModalOpen}
                  id={`${reservationDetails?.id}`}
                />
              )}
              {isInvoiceModalOpen && (
                <InvoiceModal
                  isBottomSheet={isMobile}
                  invoiceDetails={selectedInvoice as InvoiceDetailsItem}
                  setOpen={setIsInvoiceModalOpen}
                  setSelectedInvoice={setSelectedInvoice}
                  open={isInvoiceModalOpen && !!selectedInvoice}
                />
              )}
              {isInvoicesListModalOpen && (
                <InvoicesListDialog
                  isBottomSheet={isMobile}
                  open={isInvoicesListModalOpen && !isInvoiceModalOpen}
                  setOpen={setIsInvoicesListModalOpen}
                  handleInvoiceViewClick={handleViewInvoiceItemClick}
                  reservationNo={reservationDetails?.id}
                  invoiceDetails={reservationDetails?.invoices_details}
                />
              )}
            </>
          )}

          <FilterReservationModal
            open={isFilterDialogOpen}
            setOpen={setIsFilterDialogOpen}
            reservationFilter={reservationFilter}
          />
        </div>
      </Tabs>
    </div>
  );
};

export default Reservation;
