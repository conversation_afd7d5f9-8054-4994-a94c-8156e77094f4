import { createContext } from 'react';

export const ReservationContext = createContext<{
  handleCancelBtnClick: () => void;
  handleEditReservationClick: () => void;
  handleReservationAgainClick: () => void;
  handleRatingClick: () => void;
  setIsEditPending: (value: boolean) => void;
  isEditPending: boolean;
  handleViewInvoiceClick: () => void;
}>({
  handleCancelBtnClick: () => {},
  handleEditReservationClick: () => {},
  handleReservationAgainClick: () => {},
  handleRatingClick: () => {},
  setIsEditPending: () => {},
  handleViewInvoiceClick: () => {},
  isEditPending: false,
});
