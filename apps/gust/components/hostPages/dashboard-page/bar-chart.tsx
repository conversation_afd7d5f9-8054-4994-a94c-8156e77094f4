'use client';
import React, { FC, useMemo } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { Statistic } from '@/types/host-pages/dashboard';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { getLayoutDirection } from '@/lib/utils';

const RiyalIconPlugin = {
  id: 'riyalsymbol',
  beforeInit: (chart) => {
    if (!chart.riyalsymbolImg) {
      const img = new Image();
      img.src = '/fonts/currency/Saudi_Riyal_Symbol-2.svg';
      chart.riyalsymbolImg = img;
    }
  },
  afterDatasetsDraw: (chart) => {
    const ctx = chart.ctx;
    const yAxis = chart.scales.y;
    const img = chart.riyalsymbolImg;

    if (!img.complete) {
      img.onload = () => chart.draw();
      return;
    }

    yAxis.ticks.forEach((_, index) => {
      const y = yAxis.getPixelForTick(index);
      ctx.drawImage(img, yAxis.left - 12, y - 8, 12, 12);
    });
  },
};

ChartJS.register(RiyalIconPlugin);

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const colors = ['#C7C7C7', '#8CACBB', '#89A0B9', '#D84E67', '#89A0B9', '#9f9f9f', '#56789b'];
export const BarChart: FC<{ statistics: Statistic[] }> = ({ statistics = [] }) => {
  const t = useScopedI18n('hostPages.dashboardPage');
  const tCurr = useScopedI18n('currency');

  const sortedDataEn = [...statistics.slice(-1), ...statistics.slice(0, -1)];
  const sortedDataAr = [...sortedDataEn].reverse();

  const locale = useCurrentLocale();
  const direction = getLayoutDirection(locale);
  const isRTL = locale === 'ar';
  const sortedData = direction === 'rtl' ? sortedDataAr : sortedDataEn;

  const options: any = useMemo(
    () => ({
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: false,
        },
        tooltip: {
          rtl: isRTL,
          bodyFont: {
            family: 'Almarai',
          },
          titleFont: {
            family: 'Almarai',
          },

          textDirection: direction,
          callbacks: {
            title: () => '',
            label: function (tooltipItem) {
              const index = tooltipItem.dataIndex;
              return ` ${statistics[index].reservation_count} ${t('reservations')}`;
            },
          },
        },
        ...(isRTL ? { riyalsymbol: RiyalIconPlugin } : {}),
      },
      scales: {
        y: {
          position: isRTL ? 'right' : 'left',
          max: 250000,
          ticks: {
            font: {
              family: 'Almarai',
            },
            display: true,
            callback: function (value) {
              return `${value} ${!isRTL ? tCurr('sar') : ''}`;
            },
          },

          border: {
            dash: [6, 6],
            color: 'transparent',
          },

          grid: {
            drawBorder: false,
            drawTicks: false,
          },
        },
        x: {
          ticks: {
            padding: 10,
            font: {
              family: 'Almarai',
            },
          },
          grid: {
            drawOnChartArea: false,
            drawBorder: false,
            drawTicks: false,
          },
          border: {
            display: false,
          },
        },
      },
    }),
    [isRTL, direction]
  );

  const data = useMemo(
    () => ({
      labels: sortedData.map((item) => item.day_name),
      datasets: [
        {
          label: '',
          data: sortedData.map((item) => item.total_price),
          backgroundColor: sortedData.map((_, index) => colors[index % colors.length]),
          barThickness: 36,
          categoryPercentage: 0.6,
          barPercentage: 0.8,
          borderRadius: { topLeft: 8, topRight: 8 },
        },
      ],
    }),
    [sortedData]
  );

  return (
    <div style={{ width: '100%', height: '194px' }}>
      <Bar options={options} data={data} />
    </div>
  );
};
