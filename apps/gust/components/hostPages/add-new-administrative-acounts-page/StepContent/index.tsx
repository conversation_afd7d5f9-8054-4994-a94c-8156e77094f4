'use client';

import { z } from 'zod';

import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useAdministrativeAccountStore } from '../hooks/useAddingAdministrativeAccount';
import { AdministrativeStep } from '../types';
import { useSendInvitation } from '@/queries/host-pages/administrative-accounts';
import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import ChooseAddOption from '../steps/chooseAddOption';

interface StepContentProps {
  step: AdministrativeStep;
}
// Define the type of properties
type Property = {
  id: string; // Adjust the type as needed (e.g., number or optional field)
};

const StepContent = ({ step }: StepContentProps) => {
  const t = useScopedI18n('hostPages.administrative_accounts_page');
  const [openSuccess, setOpenSuccess] = useState(false);
  const [openError, setOpenError] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  const { nextStep, isLoading, activeStepIndex, steps, resetForm, resetSteps, setSteps, updateFormData } =
    useAdministrativeAccountStore();
  const { mutate: saveManager, isPending } = useSendInvitation();

  const params = new URLSearchParams(searchParams.toString());
  const specific = params.get('specific');
  const propertyId = params.get('propertyId');
  const stepLength = steps.length - 1;

  const methods = useForm({
    defaultValues: {
      role_id: '',
      first_name: '',
      last_name: '',
      phone_number: '',
      phone_code: '',
      properties: [] as Property[], // Add this type assertion
      manager_id: '',
      account_type: '',
    },
    resolver: zodResolver(step.validationSchema),
    mode: 'onChange',
  });

  const {
    watch,
    handleSubmit,
    formState: { isValid },
  } = methods;

  const accountType = watch('account_type');
  const onSubmit = () => {
    const values = watch();
    // Create a FormData instance
    const formData = new FormData();
    formData.append('role_id', values.role_id);
    values.first_name && formData.append('first_name', values.first_name);
    values.last_name && formData.append('last_name', values.last_name);
    values.phone_number && formData.append('phone_number', values.phone_number);
    values.phone_code && formData.append('phone_code', values.phone_code);
    values.manager_id && formData.append('manager_id', values.manager_id);

    // Handle specific and propertyId logic
    if (specific && propertyId) {
      formData.append('properties[0][main_id]', propertyId);
    } else if (Array.isArray(values.properties)) {
      // Only iterate over properties if specific and propertyId are not defined
      values.properties.forEach((property, index) => {
        if (property && property.id) {
          formData.append(`properties[${index}][main_id]`, property.id);
        }
      });
    }

    saveManager(formData, {
      onSuccess: () => {
        setOpenSuccess(true);
      },

      onError: (error: any) => {
        setOpenError(true);
      },
    });
  };

  const StepComponent = step.component;

  useEffect(() => {
    if (specific === 'specific') {
      const newStep: AdministrativeStep = {
        id: 'specific-step',
        title: 'Specific  Step',
        description: 'Details for the specific step',
        labelLocaleKey: 'adminAccount_info',
        component: ChooseAddOption,
        validationSchema: z.object({
          account_type: z.string(),
        }),
      };

      // Check if the step is already in the steps array
      if (!steps.find((step) => step.id === 'specific-step')) {
        setSteps([...steps.slice(0, 1), newStep, ...steps.slice(1)]);
      }
    }
  }, [searchParams, steps, setSteps]);

  const handleNextStep = () => {
    updateFormData({
      role: String(watch('role_id')) === '15' ? t('property_manager') : t('assistant_director'),
    });
    nextStep();
  };
  const handleGoToAdminPage = () => {
    resetForm();
    resetSteps();
    router.push('/host/administrativeAccounts');
  };

  const handleCloseDialog = () => {
    setOpenError(false);
    setOpenSuccess(false);
  };
  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex items-center justify-between border-b border-solid border-gray-50 px-6 py-5">
          <h2 className="text-secondary-300 text-4xl font-bold">{t(step.labelLocaleKey)}</h2>
        </div>

        <div className="min-h-40 p-4">
          <StepComponent />
        </div>

        <div className="flex justify-between px-4 pb-6">
          {accountType === 'old' || activeStepIndex === stepLength ? (
            <Button
              loading={isPending}
              type="submit"
              className="min-h-12 min-w-40 max-w-max"
              disabled={!isValid || isPending}>
              {t('send_invitation')}
            </Button>
          ) : (
            <Button onClick={handleNextStep} type="button" className="min-h-12 min-w-40 max-w-max" disabled={isLoading}>
              {t('next')}
            </Button>
          )}
        </div>

        <Dialog open={openSuccess} onOpenChange={setOpenSuccess}>
          <DialogContent className="bg-white-50 gap-0 lg:max-w-sm xl:max-w-sm">
            <DialogHeader className="flex justify-center border-none pb-0">
              <DialogTitle className="text-center text-lg">{t('dialog.successTitle')}</DialogTitle>{' '}
            </DialogHeader>
            <div className="px-6 py-4">
              <p className="text-md text-gray-400">{t('dialog.successMessage')}</p>
              <div className="flex justify-center gap-5 p-4">
                <Button className="bg-primary hover:bg-primary-400 h-12 w-full" onClick={handleGoToAdminPage}>
                  {t('continue')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={openError} onOpenChange={setOpenError}>
          <DialogContent className="bg-white-50 gap-0 lg:max-w-sm xl:max-w-sm">
            <DialogHeader className="flex justify-start border-none pb-0">
              <DialogTitle className="text-primary text-lg">لا يمكنك إضافة هذا الرقم</DialogTitle>{' '}
            </DialogHeader>
            <div className="px-6 py-4">
              <p className="text-md text-gray-400">
                هذا الرقم مسجل كحساب مضيف أو كحساب إداري مرتبط بحساب مضيف آخر على منصة رينتور ولا يمكنك إضافته كحساب
                إداري مرتبط بهذا الحساب. الرجاء إستخدام رقم جوال جديد والمحاولة مرة أخرى.
              </p>
              <div className="flex justify-center gap-5 p-4">
                <Button className="bg-primary hover:bg-primary-400 h-12 w-full" onClick={handleCloseDialog}>
                  حسناً
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </form>
    </FormProvider>
  );
};

export default StepContent;
