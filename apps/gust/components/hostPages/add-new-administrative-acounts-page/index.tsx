'use client';

import React from 'react';
import Breadcrumbs from '@/components/common/breadcrumbs';
import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useAdministrativeAccountStore } from './hooks/useAddingAdministrativeAccount';
import { Card } from '@/components/ui/card';
import StepContent from './StepContent';
import { useRouter } from 'next/navigation';

const PageHeader = () => {
  const { formData, activeStepIndex, resetSteps } = useAdministrativeAccountStore();
  const router = useRouter();

  const t = useScopedI18n('hostPages.administrative_accounts_page');

  const breadcrumbsItems = [
    { label: t('administrative_accounts'), href: '/host/administrativeAccounts' },
    { label: `${t('add_an_administrative_account')} ${!!activeStepIndex ? `- ${formData.role}` : ''}` },
  ];

  const handleBack = () => {
    const linkTo = '/host/administrativeAccounts';
    router.push(linkTo);

    setTimeout(() => {
      resetSteps();
    }, 1000);
  };

  return (
    <div className="mb-6 flex items-center justify-between">
      <Breadcrumbs items={breadcrumbsItems} />

      <Button onClick={handleBack} variant="outline-secondary" className="gap-3 text-sm" size="sm">
        {t('back_to_administrative_account')}
        <IconWrapper name="LeftArrow" size={12} />
      </Button>
    </div>
  );
};

const AddNewAdminAccount = () => {
  const { activeStepIndex, steps } = useAdministrativeAccountStore();
  const currentStep = steps[activeStepIndex];
  return (
    <div className="flex h-full min-h-full">
      <div className="max-h-svh flex-1 overflow-auto px-20  py-6">
        <PageHeader />
        <Card className="gap-0 p-0">
          <StepContent step={currentStep} />
        </Card>
      </div>
    </div>
  );
};

export default AddNewAdminAccount;
