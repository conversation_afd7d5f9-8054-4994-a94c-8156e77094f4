'use client';

import { useEffect } from 'react';
import { RadioGroup } from '@/components/hostPages/common';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useFormContext } from 'react-hook-form';
import { useGetRoleAccounts } from '@/queries/host-pages/administrative-accounts';
import SkeletonLoader from '../SkeletonLoader';
import { useSearchParams } from 'next/navigation';
import { useUserStore } from '@/store/useUserStore';

const AdministrativeOrganizationType = () => {
  const t = useScopedI18n('hostPages.administrative_accounts_page');
  const searchParams = useSearchParams();
  const { userData } = useUserStore();

  const params = new URLSearchParams(searchParams);
  const specific = params.get('specific');

  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();
  const { data, isPending } = useGetRoleAccounts();

  let roles = data?.data.data || [];
  const managerPermeation = userData?.role_type;
  const isPropertyManager = managerPermeation?.role_type === 'Property Manager';
  const isAssistantDirector = managerPermeation?.role_type === 'Assistant Director';
  // Initialize role options based on conditions
  let roleOptions: any[] = [];

  // Case 1: specific === 'specific'
  if (specific === 'specific') {
    if (isAssistantDirector) {
      roleOptions = [roles[0]]; // Only show the first role for Assistant Director
    } else {
      roleOptions = [roles[1]]; // Only show the second role if not Assistant Director
    }
  }
  // Case 2: Default case when specific is not 'specific'
  else {
    if (isAssistantDirector) {
      roleOptions = [roles[0]]; // Only show the first role for Assistant Director
    } else if (isPropertyManager) {
      roleOptions = []; // Only show the last two roles for Property Manager
    } else {
      roleOptions = roles.slice(0, 2); // Default to first two roles for others
    }
  }

  // Transform roles into radio options
  roleOptions = [
    ...roleOptions,
    {
      id: 16, // or the respective ID
      display_name: t('roles.manager.title'),
      hint: t('roles.manager.hint'),
      disabled: true,
      description: t('roles.manager.caption'),
    },
    {
      id: 17, // or the respective ID
      display_name: t('roles.writer.title'),
      hint: t('roles.writer.hint'),
      disabled: true,
      description: t('roles.writer.caption'),
    },
  ].map((role) => ({
    title: role?.display_name,
    caption: role?.description,
    value: String(role?.id), // Convert to string since form values are typically strings
    disabled: role?.disabled,
    hint: role?.hint,
  }));

  useEffect(() => {
    if (roles.length) {
      // Register the correct role based on "specific"
      setValue('role_id', specific === 'specific' ? String(roles[1]?.id) : String(roles[0]?.id));
    }
  }, [roles, specific]);

  if (isPending) {
    return <SkeletonLoader />;
  }

  return (
    <div className="m-auto flex h-full flex-col gap-4">
      <div className="text-secondary-300 text-md font-bold">{t('selectAdministrativeAccountType')}</div>
      <RadioGroup
        theme="withCaption"
        options={roleOptions}
        errMsg={errors.roleId?.message as string}
        {...register('role_id')}
      />
    </div>
  );
};

export default AdministrativeOrganizationType;
