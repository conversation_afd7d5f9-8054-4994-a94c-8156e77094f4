'use client';

import { Input } from '@/components/hostPages/common';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useFormContext } from 'react-hook-form';
import { useGetAdministrativeAccounts } from '@/queries/host-pages/administrative-accounts';
import InputTelPhoneNumber from '@/components/ui/input-tel-phone-number';
import SkeletonLoader from '../../SkeletonLoader';
import PropertiesSelection from './PropertySelection';

const InformationAdminAccount = () => {
  const t = useScopedI18n('hostPages.administrative_accounts_page');
  const { register, getValues, watch, setValue, control } = useFormContext();
  const { isLoading, isFetching, isPending } = useGetAdministrativeAccounts({ page: 1, pageSize: 15 });
  const isPropertyManager = getValues('role_id') === '15';
  const isPropertyManagerSpecific = getValues('account_type') === 'new';

  if (isPending || isFetching || isLoading) {
    return <SkeletonLoader />;
  }

  return (
    <div className="m-auto flex h-full flex-col gap-4">
      <p className="text-md text-gray-300">{t('addAdminAccountPrompt')}</p>
      <div className="grid max-w-[327px] grid-cols-1 gap-5 ">
        <div className="grid grid-cols-2 gap-3">
          <Input label={t('first_name')} placeholder={t('write_first_name')} {...register('first_name')} />
          <Input label={t('sec_name')} placeholder={t('write_sec_name')} {...register('last_name')} />
        </div>
        <InputTelPhoneNumber
          control={control}
          noError
          value={{
            phone_code: watch('phone_code'),
            phone_number: watch('phone_number'),
          }}
          onChange={(newPhone) => {
            setValue('phone_code', newPhone.phone_code);
            setValue('phone_number', newPhone.phone_number, {
              shouldValidate: true,
            });
          }}
        />
      </div>
      {isPropertyManager && !isPropertyManagerSpecific && <PropertiesSelection />}
    </div>
  );
};

export default InformationAdminAccount;
