'use client';
import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import styles from './styles.module.css';
import TabListTriggers from '@/components/common/tab-list-triggers';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import {
  useGetAllPropertyClassifications,
  useGetMyPropertiesClassificationDetails,
  useGetPropertyHostDetails,
} from '@/queries/host-pages/my-properties';
import SkeletonLoader from './skeleton-loader';
import ManageProperty from './manage-property';
import { useRouter, useSearchParams } from 'next/navigation';
import { useStoreTabsStore } from './setting-store/store';
import ShowProperty from './show-property';
import EditProperty from './edit-property';
import PageHeader from './PageHeader';
import { toast } from 'sonner';
import { useChangeQuery } from '../my-properties-page/utils';
import useTabsTriggerDetails from './hooks/useTabsTriggerDetails';

const SettingPropertyPage = ({ classificationId }) => {
  const t = useScopedI18n('hostPages.settingPropertyPage');
  const router = useRouter();
  const pageSize = 7;
  const currentLang = useCurrentLocale();
  const searchParams = useSearchParams();
  const { paramsWithValues, onChangeQuery } = useChangeQuery();
  const { PropertyType, classification_id, classification_type, type } = paramsWithValues;

  const [params] = useState(() => new URLSearchParams(searchParams.toString()));

  const [page] = useState<number>(1);
  const { isLoading, refetch, isFetching, data } = useGetMyPropertiesClassificationDetails({
    page,
    pageSize,
    type,
    classificationId,
  });

  const { data: all_classification, isFetching: classification_fetching } =
    useGetAllPropertyClassifications(classificationId);

  const {
    currentTab,
    parentName,
    handleAction,
    setParentName,
    setClassificationId,
    setRoomClassificationId,
    setUnitId,
  } = useStoreTabsStore();

  const defaultParentName = classification_id
    ? data?.data?.data?.find((item) => item.id === Number(classification_id))?.units?.[0]?.name
    : data?.data?.data?.[0]?.units?.[0]?.name;

  const room_id = classification_id ? classification_id : data?.data?.data[0]?.id;

  const unit_id = classification_id
    ? data?.data?.data?.find((item) => item.id === Number(classification_id))?.units?.[0]?.id
    : data?.data?.data?.[0]?.units?.[0]?.id;

  const is_different_classifications = classification_type === 'different_classifications';

  const is_unit_from_different_classifications = classification_type === 'unit_from_different_classifications';

  const { data: propertyData, isLoading: isPropertyLoading } = useGetPropertyHostDetails(classificationId, room_id);
  const { status, wp_status } = propertyData?.data?.data || {};
  const isStatusPending = status === 'Pending';
  const isPropertyUnderReview = wp_status === 'UnderReviewHost' || wp_status === 'UnderReviewAdmin';
  const isNavigationDisabled = isStatusPending && isPropertyUnderReview;

  const tabsTriggerDetails = useTabsTriggerDetails();

  useEffect(() => {
    if (!data?.data?.data && !isLoading && !isFetching) {
      refetch();
    }
  }, [data, isLoading, isFetching, refetch]);

  useEffect(() => {
    setParentName(defaultParentName);
    setClassificationId(classificationId);
    setRoomClassificationId(room_id);
    setUnitId(unit_id);
  }, [defaultParentName, room_id, classificationId, unit_id]);

  const handleTabChange = (value: string) => {
    if (isNavigationDisabled) return toast.warning(t('please_contact_support_to_edit_or_manage'));
    handleAction(value);
    params.set('PropertyType', value);

    onChangeQuery({ PropertyType: value });
  };
  if (isLoading || isFetching || classification_fetching || isPropertyLoading) return <SkeletonLoader />;

  return (
    <div>
      <Tabs
        value={PropertyType || currentTab}
        onValueChange={handleTabChange}
        orientation="vertical"
        className={cn('flex h-[63vh]', styles.propertiesContainer)}>
        <div
          className={
            'border-gray-75 fixed h-full w-[232px] overflow-x-hidden overflow-y-hidden border-e-[1px] border-solid px-3 py-8'
          }>
          <TabListTriggers
            items={tabsTriggerDetails}
            vertical
            containerClassName="!shadow-none !gap-3 !h-full justify-normal m-0 !p-0"
            className="data-[state=active]:text-secondary-300 data-[state=active]:border-primary-300 text-secondary-300 box-border w-full shadow-lg data-[state=active]:border-[1px]  data-[state=active]:border-solid data-[state=active]:bg-[#fff] data-[state=active]:font-normal data-[state=active]:shadow-none"
          />
        </div>
        <div className={cn(' w-full px-20 pt-8', currentLang === 'ar' ? 'mr-[210px]' : 'ml-[210px]')}>
          <PageHeader
            parentName={parentName || defaultParentName}
            room_classification_name={
              is_different_classifications
                ? ''
                : data?.data?.data?.find((item) => Number(item.id) === Number(room_id))?.classification_name
            }
            is_unit_from_different_classifications={is_unit_from_different_classifications}
            classificationId={classificationId}
            type={tabsTriggerDetails.find((item) => item.title === PropertyType)?.trans}
            classificationData={all_classification?.data?.data}
          />

          {!is_different_classifications && (
            <TabsContent className="bg-transparent" value="show">
              <ShowProperty propertyId={classificationId} roomClassificationId={room_id} />
            </TabsContent>
          )}

          <TabsContent className="bg-transparent" value="manage">
            <ManageProperty classificationId={classificationId} />
          </TabsContent>
          <TabsContent className="bg-transparent" value="edit">
            <EditProperty propertyId={classificationId} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default SettingPropertyPage;
