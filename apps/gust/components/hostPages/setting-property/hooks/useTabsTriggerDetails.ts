import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useChangeQuery } from '../../my-properties-page/utils';

const useTabsTriggerDetails = () => {
  const t = useScopedI18n('hostPages.settingPropertyPage');
  const { paramsWithValues } = useChangeQuery();
  const { classification_type, manage_unit_mode } = paramsWithValues;

  const is_multi_classifications_property = classification_type === 'different_classifications';
  const is_one_classification = classification_type === 'unit_from_different_classifications';
  const is_manage_unit_mode = manage_unit_mode == 'true';

  const propertyDifferentClassificationsTriggers = [
    { title: 'manage', trans: t('manage') },
    { title: 'edit', trans: t('edit') },
  ];

  const propertyOneUnitAndIdenticalUnitsTriggers = [
    { title: 'show', trans: t('show') },
    { title: 'manage', trans: t('manage') },
    { title: 'edit', trans: t('edit') },
  ];

  const oneClassificationTriggers = [
    { title: 'show', trans: t('show_classifications') },
    { title: 'manage', trans: t('manage_classifications') },
    { title: 'edit', trans: t('edit_classifications') },
  ];

  const manageUnitTriggers = [{ title: 'manage', trans: t('manage_unit_label') }];

  switch (true) {
    case is_manage_unit_mode:
      return manageUnitTriggers;

    case is_multi_classifications_property:
      return propertyDifferentClassificationsTriggers;

    case is_one_classification:
      return oneClassificationTriggers;

    default:
      return propertyOneUnitAndIdenticalUnitsTriggers;
  }
};

export default useTabsTriggerDetails;
