import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useChangeQuery } from '../../my-properties-page/utils';
import { useManageTabsStore } from '../setting-store/manage-tab-store';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const useBackButtonDetails = () => {
  const router = useRouter();
  const t = useScopedI18n('hostPages.settingPropertyPage');
  const { paramsWithValues, onChangeQuery, deleteQueryParam } = useChangeQuery();

  const { setCurrentTab } = useManageTabsStore();
  const [label, setLabel] = useState('');
  const [onClick, setOnClick] = useState(() => () => {});

  const { manage_unit_mode, classification_type, unit_classification_id, PropertyType } = paramsWithValues;

  const is_manage_mode = PropertyType === 'manage';
  const is_edit_mode = PropertyType === 'edit';
  const is_identical_units_property = classification_type === 'identical_units';
  const is_multi_classifications_property = classification_type === 'different_classifications';
  const is_one_classification = classification_type === 'unit_from_different_classifications';
  const is_manage_unit_mode = manage_unit_mode === 'true';

  useEffect(() => {
    switch (true) {
      case is_manage_unit_mode && is_multi_classifications_property: {
        const onClick = () => {
          deleteQueryParam(['manage_unit_mode', 'unit_id', 'unit_classification_id']);

          onChangeQuery({
            type: 'Listed',
            PropertyType: 'manage',
            classification_id: String(unit_classification_id),
            classification_type: 'unit_from_different_classifications',
          });

          setCurrentTab('setting');
        };

        setOnClick(() => onClick);
        setLabel(t('returnToManage_classifications'));
        break;
      }

      case is_manage_unit_mode && is_identical_units_property: {
        const onClick = () => {
          const newParams = new URLSearchParams();

          deleteQueryParam(['manage_unit_mode', 'unit_id', 'unit_classification_id']);
          onChangeQuery({
            type: 'Listed',
            PropertyType: 'manage',
            classification_id: String(unit_classification_id),
            classification_type: 'identical_units',
            editableActiveTab: 'details',
          });

          setCurrentTab('setting');
        };

        setLabel(t('returnToManage_property'));
        setOnClick(() => onClick);
        break;
      }

      case is_one_classification && is_manage_mode: {
        const onClick = () => {
          onChangeQuery({
            type: 'Listed',
            PropertyType: 'manage',
            classification_type: 'different_classifications',
          });
        };

        setLabel(t('returnToManage_property'));
        setOnClick(() => onClick);
        break;
      }

      case is_one_classification && is_edit_mode: {
        const onClick = () => {
          onChangeQuery({
            type: 'Listed',
            PropertyType: 'edit',
            classification_type: 'different_classifications',
            editableActiveTab: 'details',
          });
        };

        setLabel(t('returnToEdit_property'));
        setOnClick(() => onClick);
        break;
      }

      default:
        setLabel(t('returnToMyProperties'));
        setOnClick(() => () => router.push('/host/myProperties?activeTab=Listed'));
        break;
    }
  }, [
    is_manage_unit_mode,
    is_multi_classifications_property,
    is_identical_units_property,
    is_one_classification,
    is_manage_mode,
    is_edit_mode,
    PropertyType,
    classification_type,
    manage_unit_mode,
  ]);

  return { label, onClick };
};

export default useBackButtonDetails;
