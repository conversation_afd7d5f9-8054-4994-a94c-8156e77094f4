'use client';
import Breadcrumbs from '@/components/common/breadcrumbs';
import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { useChangeQuery } from '../my-properties-page/utils';
import useBackButtonDetails from './hooks/useBackButtonDetails';

const PageHeader = ({
  parentName,
  type,
  classificationId,
  is_unit_from_different_classifications,
  room_classification_name,
  classificationData,
}) => {
  const t = useScopedI18n('hostPages.settingPropertyPage');
  const currentLang = useCurrentLocale();

  const { paramsWithValues } = useChangeQuery();
  const { manage_unit_mode, classification_type, unit_classification_id, unit_id } = paramsWithValues;

  const isManageUnitMode = manage_unit_mode == 'true';
  const isMultiUnitProperty = classification_type == 'identical_units';
  const isMultiClassificationProperty = classification_type == 'different_classifications';

  const classificationName = Array.isArray(classificationData)
    ? classificationData.find((item) => item.id === Number(unit_classification_id))?.classification_name
    : '';

  let unit: any = null;
  if (Array.isArray(classificationData)) {
    for (const item of classificationData) {
      const foundUnit = item.units?.find((unitItem) => unitItem.id === Number(unit_id));
      if (foundUnit) {
        unit = foundUnit;
        break;
      }
    }
  }

  const breadcrumbsItems = [
    { label: t('myProperties'), href: '/host/myProperties' },
    {
      label: parentName || classificationData[0].units[0].name,
      ...(is_unit_from_different_classifications && {
        href: `/host/myProperties/${classificationId}?type=Listed`,
      }),
    },
    ...(room_classification_name ? [{ label: room_classification_name }] : []), // Conditionally add room_classification_name
    ...(!isManageUnitMode ? [{ label: type }] : []),
    ...(isManageUnitMode && isMultiClassificationProperty
      ? [
          { label: classificationName },
          { label: t('manage_classifications') },
          { label: `${t('units.manage')} ${t('units.unit_no')} ${unit?.sub_name}` },
        ]
      : []),

    ...(isManageUnitMode && isMultiUnitProperty
      ? [{ label: type }, { label: `${t('units.manage')} ${t('units.unit_no')} ${unit?.sub_name}` }]
      : []),
  ];

  const { onClick, label } = useBackButtonDetails();

  return (
    <div className="mb-6 flex items-center justify-between">
      <Breadcrumbs items={breadcrumbsItems} />

      <Button onClick={onClick} variant="outline-secondary" className="text-md gap-3">
        {label}
        <IconWrapper name={currentLang === 'ar' ? 'LeftArrow' : 'RightArrow'} size={12} />
      </Button>
    </div>
  );
};

export default PageHeader;
