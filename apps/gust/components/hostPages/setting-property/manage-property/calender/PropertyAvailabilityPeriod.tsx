import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useManagePrice, useSavePrice } from '@/queries/host-pages/manage-properties';
import { useStoreTabsStore } from '../../setting-store/store';
import { useEffect, useState } from 'react';
import { parse, format } from 'date-fns';
import SuccessDialog from '../classifications/SuccessDialog';

const PropertyAvailabilityPeriod = () => {
  const t = useScopedI18n('hostPages.settingPropertyPage.propertyAvailabilityPeriod');
  const [selectedValue, setSelectedValue] = useState('');
  const [isChanged, setIsChanged] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [openSuccess, setOpenSuccess] = useState(false);
  const availabilityPeriodList = [
    {
      label: t('_3_months'),
      value: '3_months',
      months: 3,
    },
    {
      label: t('_6_months'),
      value: '6_months',
      months: 6,
    },
    {
      label: t('_9_months'),
      value: '9_months',
      months: 9,
    },
    {
      label: t('_12_months'),
      value: '12_months',
      months: 12,
    },
    {
      label: t('_3_years'),
      value: '3_years',
      months: 36,
    },
  ];

  const convertDateTimeDisplay = (date: string, type?: string): string => {
    if (type === 'server') {
      const parsedDate = parse(date, 'dd/MM/yyyy', new Date());
      const formattedDate = format(parsedDate, 'dd-MM-yyyy');
      return formattedDate;
    }

    const parsedDate = parse(date, 'yyyy-MM-dd', new Date());
    const formattedDate = format(parsedDate, 'dd/MM/yyyy');
    return formattedDate;
  };

  const updateServerDate = (date: string) => {
    const parsedDate = parse(date, 'dd/MM/yyyy', new Date());
    const formattedDate = format(parsedDate, 'yyyy-MM-dd');
    return formattedDate;
  };

  const { classificationId, roomClassificationId } = useStoreTabsStore();

  const { mutate, isPending, isSuccess } = useSavePrice(classificationId, '');
  let { data, loading } = useManagePrice(classificationId, '') as any;

  function getNumberOfMonthsBetweenDates(startDateStr: string, endDateStr: string): number {
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth();
    const totalMonths = (endYear - startYear) * 12 + (endMonth - startMonth);

    if (Number.isNaN(totalMonths)) {
      return 0;
    }

    return totalMonths;
  }

  function getAvailabilityPeriodIndex(months: number): number {
    const index = availabilityPeriodList.findIndex((period) => period.months === months);
    return index;
  }
  const onValueChange = (value: string) => {
    setSelectedValue(value);

    // change the end data to the start data plus the number of months
    const index = availabilityPeriodList.findIndex((period) => period.value === value);
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + availabilityPeriodList[index].months);
    setEndDate(format(endDate, 'dd/MM/yyyy'));
    setIsChanged(true);
  };

  useEffect(() => {
    if (loading) return;

    if (data?.data.data?.start_date && data?.data.data?.end_date) {
      setStartDate(convertDateTimeDisplay(data?.data.data?.start_date));
      setEndDate(convertDateTimeDisplay(data?.data.data?.end_date));
    }
    const index = getAvailabilityPeriodIndex(
      getNumberOfMonthsBetweenDates(data?.data.data?.start_date, data?.data.data?.end_date)
    );
    if (index === -1) return;

    setSelectedValue(availabilityPeriodList[index]?.value);
  }, [data]);

  useEffect(() => {
    if (isSuccess) {
      setIsChanged(false);
      if (endDate) {
        data.data.data.end_date = updateServerDate(endDate);
      }
    }
  }, [isSuccess]);

  return (
    <div className="flex h-full flex-col">
      <h2 className="text-secondary-300 text-base font-bold">{t('property_availability_period')}</h2>
      <p className="mt-3 text-[14px] text-gray-400">{t('you_can_select_a_period')}</p>
      <p className="mt-3 text-[14px] text-gray-400">
        <span className="text-primary-300">{t('note')}: </span>
        {t('change_period_note')}
      </p>
      <h2 className="text-secondary-300 mt-3 text-sm font-bold">{t('availability_from')}</h2>
      <Select value={selectedValue} onValueChange={onValueChange}>
        <SelectTrigger className="text-black-300 mt-3 h-12 max-w-xs rounded-full p-4 text-start">
          <SelectValue placeholder="" />
        </SelectTrigger>
        <SelectContent>
          {availabilityPeriodList.map((item) => (
            <SelectItem key={item.value} value={item.value}>
              {item.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <p className="mt-3 pb-4 text-[14px] text-gray-400">
        {t('availability_from_to')} {startDate} <span>{t('to')}</span> {endDate}
      </p>
      <div className="flex-1"></div>
      <Separator className="bg-borderColor absolute bottom-16 left-0 right-0 h-[1px] w-auto" />

      <div className="flex justify-end pt-4">
        <Button
          className="min-w-[143px]"
          size="lg"
          disabled={!isChanged || isPending}
          loading={loading || isPending}
          onClick={() => {
            if (isChanged) {
              mutate(
                { end_date: convertDateTimeDisplay(endDate, 'server') },
                { onSuccess: () => setOpenSuccess(true) }
              );
            }
          }}>
          {t('save')}
        </Button>
      </div>

      <SuccessDialog {...{ openSuccess, setOpenSuccess }} />
    </div>
  );
};

export default PropertyAvailabilityPeriod;
