import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { format, parse } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import IconWrapper from '@/components/icons';
import SearchTriggerButton from '@/components/header/search-bar/search-trigger-button';
import { savePriceByCalender } from '@/queries/manage-properties/save-price-by-calender';
import { toast } from 'sonner';
import CalenderLoader from '../price/calenderPriceSelector/calender-loader';
import { useStoreTabsStore } from '../../setting-store/store';
import { useGetPriceByCalender } from '@/queries/manage-properties/price-calender';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useSearchParams } from 'next/navigation';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';

import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useSettingsTabStore } from '../settings-tab-store';

const AvailabilityBySpecificDates = () => {
  const searchParams = useSearchParams();

  const { setIsSettingsDirty, isModalOpen, setIsModalOpen, setSkipSave } = useSettingsTabStore();

  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  const { sendGAEvent } = useGAEvent();

  const currentLocale = useCurrentLocale();
  const t = useScopedI18n('hostPages.settingPropertyPage.calenderTap');

  const [currentDate, setCurrentDate] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  });

  const { classificationId, roomClassificationId } = useStoreTabsStore();

  const { data, isLoading, refetch, isRefetching } = useGetPriceByCalender({
    id: classificationId,
    room_classification_id: roomClassificationId,
    year: currentDate.year,
    month: currentDate.month,
    unit_id: searchParams?.get('unit_id'),
  });
  const [currentTitle, setCurrentTitle] = useState('');
  const calendarRef = useRef<any>(null);

  const [selectedDates, setSelectedDates] = useState<any>([]);
  const [availability, setAvailability] = useState<any>('');

  const deleteSelectedDates = () => {
    setSelectedDates([]);
    setIsSettingsDirty(false);
  };

  const handlePrev = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.prev();
    }
  };

  const handleNext = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.next();
    }
  };
  const updateTitle = (calendarApi) => {
    const date = calendarApi.getDate();
    const formattedDate = format(date, 'MMMM yyyy', { locale: currentLocale === 'ar' ? ar : enUS });
    setCurrentTitle(formattedDate);

    const updatedMonth = date.getMonth() + 1;

    if (updatedMonth > currentDate.month + 1 || updatedMonth > currentDate.month + 2) {
      setCurrentDate({
        month: updatedMonth,
        year: date.getFullYear(),
      });
    }
    if (updatedMonth < currentDate.month) {
      setCurrentDate({
        month: updatedMonth,
        year: date.getFullYear(),
      });
    }
  };

  const { mutate, isPending } = savePriceByCalender({
    id: classificationId,
    room_classification_id: roomClassificationId,
    unit_id: searchParams?.get('unit_id') || '',
  });

  const handleOnSelectChange = (selectedDays) => {
    setSelectedDates(selectedDays);
    setIsSettingsDirty(true);
  };

  const formatServerDate = (date) => {
    const parsedDate = parse(date, 'dd/MM/yyyy', new Date());
    const formattedDate = format(parsedDate, 'dd-MM-yyyy');
    return formattedDate;
  };

  const handleOnApply = () => {
    sendGAEvent({ action: 'calendar_saved', property_id: classificationId });
    if (selectedDates.length === 0) {
      toast.error(t('select_dates_first'));
      setIsModalOpen(false);
      return;
    }

    if (availability === '') {
      toast.error(t('select_availability_state_first'));
      setIsModalOpen(false);
      return;
    }

    mutate(
      {
        start_date: formatServerDate(selectedDates[0]),
        end_date: formatServerDate(selectedDates[selectedDates.length - 1]),
        status: availability,
      },
      {
        onSuccess: () => {
          toast.success(t('updated_successfully'));
          setSelectedDates([]);
          setIsSettingsDirty(false);
          setAvailability('');
          refetch();
        },
        onError: () => {
          toast.error(t('error_in_updating'));
        },
      }
    );
  };

  const handleOnSave = () => {
    handleOnApply();
  };

  const handleOnSkip = () => {
    setIsSettingsDirty(false);
    setIsModalOpen(false);
    setSkipSave(true);
  };
  return (
    <>
      <div>
        <div className="mb-4 flex items-center justify-center gap-x-8">
          <div className="flex items-center gap-x-2">
            <div className="bg-white-400 h-[6px] w-[6px] rounded-full"></div>
            <p>{t('unavailable')}</p>
          </div>
          <div className="flex items-center gap-x-2">
            <div className="bg-primary-300 h-[6px] w-[6px] rounded-full"></div>
            <p>{t('booked')}</p>
          </div>
          <div className="flex items-center gap-x-2">
            <div className="bg-success-300 h-[6px] w-[6px] rounded-full"></div>
            <p>{t('available')}</p>
          </div>
        </div>
        {isLoading || isRefetching ? (
          <CalenderLoader />
        ) : (
          <div
            style={{ '--fc-border-color': '#F5F5F5' } as React.CSSProperties}
            className="rounded-xs shadow-[0px_2px_8px_0px_#00000014]">
            <div className="flex items-center justify-between p-3">
              <Button type="button" variant="ghost" className="customPrev" onClick={handlePrev}>
                <IconWrapper
                  name={currentLocale === 'ar' ? 'RightArrow' : 'LeftArrow'}
                  size={20}
                  className="text-secondary-300"
                />
              </Button>
              <p className="text-secondary-300 text-sm font-bold">{currentTitle}</p>

              <Button type="button" variant="ghost" className="customNext" onClick={handleNext}>
                <IconWrapper
                  name={currentLocale === 'ar' ? 'LeftArrow' : 'RightArrow'}
                  size={20}
                  className="text-secondary-300"
                />
              </Button>
            </div>

            <FullCalendar
              validRange={{ start: new Date(today.getFullYear(), today.getMonth(), 1) }}
              initialDate={new Date(`${currentDate.year}-${currentDate.month}-01`)}
              ref={calendarRef}
              viewClassNames={'border-red-500'}
              plugins={[dayGridPlugin, interactionPlugin]}
              initialView="dayGridMonth"
              headerToolbar={false}
              selectable={true}
              selectAllow={({ start }) => {
                const format2 = format(start, 'yyyy-MM-dd');
                let allow = true;
                if (start < yesterday) {
                  allow = false;
                }
                const day = data?.data?.find((item) => item.date === format2);
                if (day?.status === 'Not available') {
                  allow = false;
                }

                return allow;
              }}
              datesSet={(arg) => {
                const calendarApi = arg.view.calendar;
                updateTitle(calendarApi);
              }}
              customButtons={{
                customNext: {
                  click: handleNext,
                },
                customPrev: {
                  click: handlePrev,
                },
              }}
              locale={currentLocale}
              direction={currentLocale === 'ar' ? 'rtl' : 'ltr'}
              // days header
              dayHeaderClassNames={'text-secondary bg-[#FAFAFC] border-gray-50 h-full w-full text-sm font-bold'}
              // day cell
              dayCellClassNames={(args) => {
                const formattedDate = format(args.date, 'dd/MM/yyyy');
                const format2 = format(args.date, 'yyyy-MM-dd');

                let classes: string[] = [];

                if (selectedDates.length > 0) {
                  if (formattedDate === selectedDates[0]) classes.push('custom-selected-start');
                  if (formattedDate === selectedDates[selectedDates.length - 1]) classes.push('custom-selected-end');
                  if (selectedDates.includes(formattedDate)) classes.push('custom-selected');
                }

                // set class fc-custom-disabled-days for all past days from today
                if (args.date < yesterday) {
                  classes.push('fc-custom-disabled-days');
                }

                // set class fc-custom-disabled-days for all days that are not available
                const day = data?.data?.find((item) => item.date === format2);
                if (day?.status === 'Not available') {
                  classes.push('fc-custom-disabled-days');
                }

                return `${classes.join(' ')} bg-white-50 border-red-500 border`;
              }}
              dayCellContent={(args) => {
                const formattedData = format(args.date, 'yyyy-MM-dd');
                const day = data?.data?.find((item) => item.date === formattedData);
                return (
                  <div className={`text-black-400 flex items-center justify-between gap-x-4 text-[14px]`}>
                    <span>{args.date.getDate()}</span>

                    {day?.status === 'Not available' && day?.className.includes('status-b') && (
                      <span className="bg-white-400 h-1 w-1 rounded-full"></span>
                    )}
                    {day?.status === 'Not available' && day?.className.includes('status-r') && (
                      <span className="bg-primary-300 h-1 w-1 rounded-full"></span>
                    )}
                    {day?.status === 'Available' && <span className="bg-success-300 h-1 w-1 rounded-full"></span>}
                  </div>
                );
              }}
              // selected days
              select={(selection) => {
                const startDate = new Date(selection.start);
                const endDate = new Date(selection.end);
                const selectedDays: string[] = [];

                while (startDate < endDate) {
                  selectedDays.push(format(startDate, 'dd/MM/yyyy'));
                  startDate.setDate(startDate.getDate() + 1);
                }

                handleOnSelectChange(selectedDays);
              }}
              // events
              events={data?.data}
              eventBorderColor="transparent"
              eventBackgroundColor="transparent"
              eventContent={(e) => {
                return renderEventContent(
                  e,
                  currentLocale === 'ar' ? (
                    <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                  ) : (
                    t('sar')
                  )
                );
              }}
            />
          </div>
        )}

        <div className=" mt-4 flex max-w-md flex-col gap-y-4 pb-10">
          <div className="relative flex max-w-md items-center gap-x-3">
            <p className="text-secondary text-lg font-semibold">{t('from')}</p>
            <SearchTriggerButton
              className="border-secondary-50 bg-input-bg-300 w-40 flex-1 flex-row justify-start rounded-lg border"
              classNameIcon="bg-secondary-50 w-12 h-12 flex justify-center items-center rtl:rounded-r-lg ltr:rounded-l-lg"
              icon={<IconWrapper className="text-secondary" name="Calendar" size={24} />}
              topParagraph={selectedDates?.length > 0 ? selectedDates[0] : ''}
            />
            <p className="text-secondary text-lg font-semibold">{t('to')}</p>
            <SearchTriggerButton
              className="border-secondary-50 bg-input-bg-300 w-40 flex-1 flex-row justify-start rounded-lg border"
              classNameIcon="bg-secondary-50 w-12 h-12 flex justify-center items-center rtl:rounded-r-lg ltr:rounded-l-lg"
              icon={<IconWrapper className="text-secondary" name="Calendar" size={24} />}
              topParagraph={selectedDates?.length > 0 ? selectedDates[selectedDates.length - 1] : ''}
            />
            {selectedDates?.length > 0 && (
              <Button
                variant="ghost"
                type="button"
                size="sm"
                className="text-primary-300 absolute top-1/2  -translate-y-1/2 gap-2 text-sm ltr:left-full rtl:right-full"
                onClick={deleteSelectedDates}>
                <IconWrapper name="Trash" size={20} className="text-primary-300" />
                {t('delete_selected_dates')}
              </Button>
            )}
          </div>
          <div className="flex items-center justify-between">
            <p className="text-secondary-300 text-[14px] font-bold">{t('selected_period_status')}</p>
            <div>
              <RadioGroup
                value={availability}
                className="flex gap-x-6"
                onValueChange={(value) => {
                  setAvailability(value);
                }}>
                <div className="flex items-center gap-x-2">
                  <RadioGroupItem value="Available" id="available" />
                  <Label htmlFor="available">{t('available_list')}</Label>
                </div>

                <div className="flex items-center gap-x-2">
                  <RadioGroupItem value="Not available" id="unavailable" />
                  <Label htmlFor="unavailable">{t('unavailable_list')}</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <Button
            type="button"
            size="lg"
            loading={isPending || isLoading}
            disabled={isPending || isLoading}
            variant="outline-secondary"
            className="border-secondary-300 w-full max-w-36"
            onClick={handleOnApply}>
            {t('apply')}
          </Button>
        </div>

        <Separator className="bg-borderColor absolute bottom-16 left-0 right-0 h-[1px] w-auto" />

        <div className="flex justify-end">
          <Button className="min-w-[143px]" size="lg" disabled={true}>
            {t('save')}
          </Button>
        </div>
      </div>
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-white-50 gap-0 lg:max-w-sm xl:max-w-sm">
          <DialogHeader className="border-none pb-0">
            <DialogTitle>{t('save_changes')}</DialogTitle>
            <DialogClose onClick={() => setIsModalOpen(false)} />
          </DialogHeader>

          <div className="px-6 py-4">
            <p className="text-md text-gray-400">
              <p className="inline-block"> {t('you_dont_save_changes')}</p>
              <p className="text-primary text-bold text-md inline-block px-1">{t('calender')}</p>
              <span className="inline">{t('are_you_sure')}</span>
            </p>
            <div className="flex justify-center gap-5 p-4">
              <Button
                loading={isPending}
                className="bg-primary hover:bg-primary-400 h-12 w-full"
                onClick={handleOnSave}>
                {t('save_changes')}
              </Button>
              <Button className="bg-gray h-12 w-full hover:bg-gray-300" onClick={handleOnSkip}>
                {t('move')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

const renderEventContent = (eventInfo: any, t) => {
  // const currentLang = useCurrentLocale();
  return (
    <div className="custom-event text-center text-sm text-gray-400">
      <div className="mx-2 flex w-full items-center justify-center gap-y-2 lg:my-2">
        <div className="price">
          {eventInfo.event.extendedProps.price} {t}
        </div>
      </div>
    </div>
  );
};

export default AvailabilityBySpecificDates;
