import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { toast } from 'sonner';
import EmptyTableCalender from './EmptyTableCalender';
import { useEffect, useState } from 'react';
import AddCalenderDialog from './AddCalenderDialog';
import ActionsBtns from './ActionsBtns';
import { useStoreTabsStore } from '../../setting-store/store';
import { useGetSyncCalenders } from '@/queries/manage-properties/get-sync-calenders';
import { format, parse } from 'date-fns';
import { useDeleteCalender } from '@/queries/manage-properties/delete-calender';
import { useSyncCalender } from '@/queries/manage-properties/sync-calender';
import TableSkeletonLoader from '@/components/common/skeleton-loaders/table';
import TableLoader from './TableLoader';
import { useSearchParams } from 'next/navigation';

const SyncCalendarWithOtherSites = () => {
  const t = useScopedI18n('hostPages.settingPropertyPage.syncCalendarWithOtherSites');
  const searchParams = useSearchParams();
  const [addCalenderDialog, setAddCalenderDialog] = useState(false);
  const { classificationId, roomClassificationId } = useStoreTabsStore();

  const unitId = searchParams?.get('unit_id') || '';
  console.log({
    roomClassificationId,
    unitId,
  });

  let { data, refetch, isFetching } = useGetSyncCalenders({
    id: classificationId,
    unitId: unitId,
  });

  const { mutate: deleteCalender } = useDeleteCalender({
    id: classificationId,
    room_id: unitId,
  });

  const { mutate: syncCalender } = useSyncCalender({
    id: classificationId,
    room_id: unitId,
  });

  const onOpenDialog = () => {
    setAddCalenderDialog(true);
  };

  const onCloseDialog = (e, isNewCalenderAdded = false) => {
    setAddCalenderDialog(false);
    if (isNewCalenderAdded) {
      refetch();
    }
  };

  function formatDate(inputDateString) {
    const parsedDate = parse(inputDateString, 'yyyy-MM-dd HH:mm:ss', new Date());
    const formattedDate = format(parsedDate, 'yyyy/MM/dd hh:mm a');
    return formattedDate;
  }

  const onDeleteCalender = (id) => {
    deleteCalender(
      {
        calender_id: id,
      },
      {
        onSuccess: () => {
          toast.success(t('calender_deleted'));
          refetch();
        },
        onError: () => {
          toast.error(t('calender_deleted_failed'));
        },
      }
    );
  };

  const onSyncCalender = (id) => {
    syncCalender(
      {
        calender_id: id,
      },
      {
        onSuccess: () => {
          toast.success(t('calender_synced'));
        },
        onError: () => {
          toast.error(t('calender_synced_failed'));
        },
      }
    );
  };

  return (
    <>
      <div className="flex h-full flex-col">
        <h2 className="text-secondary-300 text-base font-bold">{t('unit_calendar_link')}</h2>
        <p className="mt-3 text-[14px] text-gray-400">{t('unit_calendar_link_note')}</p>

        {isFetching ? (
          <div className="rounded-xs mt-3 w-full bg-gray-50 p-4"></div>
        ) : (
          <div className="rounded-xs mt-3 flex justify-between border-[0.8px] border-gray-50 p-3">
            <p className="text-secondary-300 flex-1 text-sm">{`${data?.data?.property_link}`}</p>
            <div
              className="cursor-pointer"
              onClick={() => {
                navigator.clipboard.writeText(`${data?.data?.property_link}`);
                toast.success(t('link_copied'));
              }}>
              <IconWrapper name="DocumentCopy" size={20} className="text-secondary-300" />
            </div>
          </div>
        )}
        <Separator className="my-4" />
        <div className="flex items-center justify-between">
          <p className="text-secondary-300 text-[14px] font-bold">{t('calender_synced_with')}</p>
          <Button variant="outline" className="text-secondary-300 border-secondary-300" onClick={onOpenDialog}>
            {t('sync_with_other_apps')}
          </Button>
        </div>

        {/* table */}
        <Table className="mt-4">
          <TableHeader>
            <TableRow>
              <TableHead className="px-3">#</TableHead>
              <TableHead className="pe-3 text-start">{t('app_name')}</TableHead>
              <TableHead className="pe-3 text-start">{t('calender_link')}</TableHead>
              <TableHead className="pe-3 text-start">{t('last_update_time')}</TableHead>
              <TableHead className="pe-3 text-start">{t('actions')}</TableHead>
            </TableRow>
          </TableHeader>

          {isFetching ? (
            <TableLoader />
          ) : (
            <>
              {data?.data?.links?.length !== 0 && (
                <>
                  <TableBody>
                    {data?.data?.links?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="px-3 text-gray-400">{item?.id}</TableCell>
                        <TableCell className="pe-3 text-start">
                          <p className="text-secondary-300 font-bold">{item?.name}</p>
                        </TableCell>
                        <TableCell className="pe-3">
                          <p className="line-clamp-1 text-start align-middle text-gray-400 underline [line-break:anywhere]">
                            {item?.url}
                          </p>
                        </TableCell>
                        <TableCell className="pe-3 text-start text-gray-400">
                          <p className="line-clamp-1 text-start align-middle">{formatDate(item?.last_sync)}</p>
                        </TableCell>
                        <TableCell className="pe-3 text-start">
                          <ActionsBtns
                            onDeleteClick={() => {
                              onDeleteCalender(item?.id);
                            }}
                            onSyncClick={() => {
                              onSyncCalender(item?.id);
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </>
              )}
            </>
          )}
        </Table>
        {data?.data?.links?.length !== 0 && (
          <Button variant="ghost" className="text-secondary-300 mt-4 flex w-fit gap-x-2" onClick={onOpenDialog}>
            <IconWrapper name="AddCircle" size={20} />
            {t('add_sync_calender')}
          </Button>
        )}

        {data?.data?.links?.length === 0 && <EmptyTableCalender handleOnClick={onOpenDialog} />}

        <div className=" flex-1 pb-10" />
        <Separator className="bg-borderColor absolute bottom-16 left-0 right-0 h-[1px] w-auto" />

        <div className="flex justify-end">
          <Button className="min-w-[143px]" size="lg" disabled={true}>
            {t('save')}
          </Button>
        </div>
      </div>
      <AddCalenderDialog
        isOpen={addCalenderDialog}
        onCLose={onCloseDialog}
        t={t}
        id={classificationId}
        room_id={unitId}
      />
    </>
  );
};

export default SyncCalendarWithOtherSites;
