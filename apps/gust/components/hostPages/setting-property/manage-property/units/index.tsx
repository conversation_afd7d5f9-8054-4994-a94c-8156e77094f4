import { Card } from '@/components/ui/card';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useStoreTabsStore } from '../../setting-store/store';
import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { useGetPropertyUnits } from '@/queries/manage-properties/get-property-units';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

const Units = () => {
  const t = useScopedI18n('hostPages.settingPropertyPage.units');
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const { roomClassificationId, classificationId } = useStoreTabsStore();
  const { data, isFetching } = useGetPropertyUnits({
    main_id: classificationId,
    room_id: roomClassificationId,
  });

  const handleOnManageUnit = (unit) => {
    const params = new URLSearchParams(searchParams);
    params.set('manage_unit_mode', 'true');
    params.set('unit_id', unit?.id);
    params.set('unit_classification_id', unit?.room_classification_id);
    if (searchParams?.get('classification_type') === 'unit_from_different_classifications') {
      params.set('classification_type', 'different_classifications');
    }
    const url = `${pathname}?${params.toString()}`;
    router.push(url);
  };

  return (
    <Card className="gap-0 pb-4 pt-6 shadow-lg">
      <div className="px-6">
        <h2 className="text-secondary-300 text-base font-bold">{t('units_details')}</h2>

        {isFetching ? (
          <div className="mt-4 flex flex-col gap-y-4">
            <div className="h-12 w-full rounded-sm bg-gray-50"></div>
            <div className="h-12 w-full rounded-sm bg-gray-50"></div>
            <div className="h-12 w-full rounded-sm bg-gray-50"></div>
          </div>
        ) : (
          <div className="mt-4 flex w-full flex-col rounded-[12px] border border-gray-50">
            {data?.data?.map((unit, index) => (
              <>
                <div className="m-3 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex flex-col gap-x-11">
                      <p className="text-black-300">
                        {t('unit_no')} {index + 1}
                      </p>
                      <p className="text-gray">#{unit?.id}</p>
                    </div>
                    <div className="text-black-300 border-input-bg-300 ms-11 flex min-w-20 items-center justify-center rounded-full border bg-gray-50 p-4">
                      {unit?.sub_name}
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    className="text-secondary-200  gap-x-2"
                    size="sm"
                    onClick={() => {
                      handleOnManageUnit(unit);
                    }}>
                    <IconWrapper name="Setting2" size={16} />
                    {t('manage')}
                  </Button>
                </div>
                {index !== data?.data?.length - 1 && <div className="h-px w-full bg-gray-50"></div>}
              </>
            ))}
          </div>
        )}
      </div>

      <div className="bg-borderColor mt-6 h-[0.6px] w-full"></div>
      <div className="mt-4 flex justify-end">
        <Button className="mx-6  min-w-[143px]" size="lg" disabled={true}>
          {t('save')}
        </Button>
      </div>
    </Card>
  );
};

export default Units;
