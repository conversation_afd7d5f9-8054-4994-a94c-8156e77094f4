import { Card } from '@/components/ui/card';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useStoreTabsStore } from '../../setting-store/store';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import TabListTriggers from '@/components/common/tab-list-triggers';
import { Button } from '@/components/ui/button';
import { useGetAllPropertyClassifications } from '@/queries/host-pages/my-properties';
import TabContent from './tab-content';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useDeletePropertyClassification } from '@/queries/host-pages/edit-property';
import { EDIT_PROPERTY_TABS_IDS } from '../../types';
import { toast } from 'sonner';
import SuccessDialog from './SuccessDialog';
import ConfirmNavigationDialog from './ConfirmNavigationDialog';
import { useNavigationGuard } from 'next-navigation-guard';
import { useManageTabsStore } from '../../setting-store/manage-tab-store';
import { useSettingsStore } from '../../setting-store/setting-store';

const Classifications = () => {
  const t = useScopedI18n('hostPages.settingPropertyPage.classifications');
  const ta = useScopedI18n('hostPages.addNewPropertyPage');

  const { classificationId } = useStoreTabsStore();
  const [localData, setLocalData] = useState<any>([]);
  const [openSuccess, setOpenSuccess] = useState(false);
  const [activeClassificationTab, setActiveClassificationTab] = useState<any>(localData[0]?.title);
  const { data, isFetching } = useGetAllPropertyClassifications(classificationId);
  const { backendDeletedClassificationsIds, setBackendDeletedClassificationsIds } = useSettingsStore();
  const classificationData = data?.data?.data;

  const { isPending: isClassificationPending, mutateAsync: deletePropertyClassification } =
    useDeletePropertyClassification({
      propertyId: classificationId,
    });

  const activeClassification = localData?.find((item) => item?.title === activeClassificationTab);

  const propertyStatus = {
    Listed: { label: ta('active'), bgColor: '#E3F5E0' },
    Pending: { label: ta('under_review'), bgColor: '#FFF1BF' },
    Draft: { label: ta('draft'), bgColor: '#F4F4F4' },
  }[activeClassification?.status];

  const formMethods = useForm();
  const { getValues, setValue } = formMethods;
  const allValues = getValues();

  const deletedClassificationsIds = Object.keys(allValues?.classifications?.deletedClassifications || {});
  const isSaveButtonEnabled = deletedClassificationsIds.length > 0;
  const { setIsDirty, setShowDialog, handleSkipSave } = useManageTabsStore();
  const { accept, active, reject } = useNavigationGuard({ enabled: isSaveButtonEnabled });

  const handleSkipConfirmation = () => {
    setShowDialog(false);
    setIsDirty(false);
    accept();
    handleSkipSave();
    setValue(`${EDIT_PROPERTY_TABS_IDS.CLASSIFICATIONS}.deletedClassifications`, {});
  };

  const onSave = async ({
    showSuccessModal,
    closeConfirmDialog,
  }: {
    showSuccessModal?: boolean;
    closeConfirmDialog?: boolean;
  }) => {
    const deletedClassifications = Object.entries(allValues?.classifications?.deletedClassifications || {}).map(
      ([key, value]) => ({
        ...(value || {}),
        classificationId: key,
      })
    );

    let filteredDeletedClassifications = deletedClassifications?.filter(Boolean) || [];

    for (let i = 0; i < deletedClassifications.length; i++) {
      const { classificationId, ...currentClassification } = filteredDeletedClassifications[i];

      try {
        const response = await deletePropertyClassification(
          {
            // reason and other to fix ts issue
            reason: '',
            other: '',
            classificationId,
            ...currentClassification,
          },
          {
            onSuccess: () => {
              setBackendDeletedClassificationsIds([classificationId]);
            },
          }
        );

        if (response?.data?.message) toast.success(response.data.message);
      } catch (error) {
        if (error?.response?.data?.message) toast.error(error.response.data.message);
      }
    }

    if (showSuccessModal) {
      setValue(`${EDIT_PROPERTY_TABS_IDS.CLASSIFICATIONS}.deletedClassifications`, {});
      setOpenSuccess(true);
    }

    if (closeConfirmDialog) {
      handleSkipConfirmation();
    }
  };

  useEffect(() => {
    const newData = classificationData
      ?.filter(
        (item) =>
          !deletedClassificationsIds.includes(String(item.id)) &&
          !backendDeletedClassificationsIds.includes(String(item.id))
      )
      ?.map((tab) => ({
        title: tab?.id,
        trans: tab?.classification_name,
        ...tab,
      }));

    setLocalData(newData);
    setActiveClassificationTab(newData[0]?.title);
  }, [deletedClassificationsIds?.length, backendDeletedClassificationsIds?.length, classificationData.length]);

  return (
    <Card className="gap-0 pb-4 pt-6 shadow-lg">
      <FormProvider {...formMethods}>
        {isFetching ? (
          <div className="mx-6  flex flex-col gap-y-4">
            <div className=" h-12 w-36 animate-pulse rounded-full bg-gray-100"></div>
            <div className=" h-12 w-36 animate-pulse rounded-full bg-gray-100"></div>
            <div className=" h-12 w-36 animate-pulse rounded-full bg-gray-100"></div>
          </div>
        ) : (
          <>
            {localData?.length > 0 && (
              <Tabs
                defaultValue={localData[0].title}
                value={activeClassificationTab}
                onValueChange={setActiveClassificationTab}
                orientation="vertical"
                className=" flex">
                <div className="h-full max-h-[50.794vh] overflow-y-auto overflow-x-hidden border-e-[1px] border-solid border-gray-50 px-6 pb-6">
                  <div className="mb-3 flex items-center justify-between">
                    <div className="text-secondary-300 text-md font-bold">{t('classifications')}</div>

                    {Boolean(propertyStatus) && (
                      <div
                        className="text-secondary-300 text-md rounded-[8px] px-2 py-1"
                        style={{ background: propertyStatus?.bgColor }}>
                        {propertyStatus?.label}
                      </div>
                    )}
                  </div>
                  <TabListTriggers
                    items={localData}
                    vertical
                    containerClassName="!shadow-none !gap-3 !h-full justify-normal m-0 !p-0"
                    className="data-[state=active]:text-secondary-300 data-[state=active]:border-primary-300 text-secondary-300 box-border w-full shadow-lg data-[state=active]:border-[1px] data-[state=active]:border-solid data-[state=active]:bg-[#fff] data-[state=active]:font-normal data-[state=active]:shadow-none"
                  />
                </div>
                {localData.map((tab) => (
                  <TabsContent key={tab?.title} className="flex-1 bg-transparent px-6" value={tab?.title}>
                    <TabContent data={tab} />
                  </TabsContent>
                ))}
              </Tabs>
            )}

            <div className="bg-borderColor mt-6 h-[0.6px] w-full"></div>
            <div className="mt-4 flex justify-end">
              <Button
                className="mx-6  min-w-[143px]"
                size="lg"
                disabled={!isSaveButtonEnabled || isClassificationPending}
                onClick={() => onSave({ showSuccessModal: true })}
                loading={isClassificationPending}>
                {t('save')}
              </Button>
            </div>
          </>
        )}

        <SuccessDialog openSuccess={openSuccess} setOpenSuccess={setOpenSuccess} />
        <ConfirmNavigationDialog
          isLoading={isClassificationPending}
          {...{ handleSkipConfirmation, isSaveButtonEnabled, onSave, active, reject }}
        />
      </FormProvider>
    </Card>
  );
};

export default Classifications;
