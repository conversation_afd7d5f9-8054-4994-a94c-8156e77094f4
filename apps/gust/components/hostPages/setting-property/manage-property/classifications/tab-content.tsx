import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import ClassificationActionButtons from './ClassificationActionButtons';
import { toast } from 'sonner';

const TabContent = ({ data }) => {
  const t = useScopedI18n('hostPages.settingPropertyPage.units');

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const { status, classification_name, id: classification_id, units } = data || {};
  const propertyId = data?.units?.[0]?.main_id;

  const handleOnManageUnit = (unit) => {
    const params = new URLSearchParams(searchParams);
    params.set('manage_unit_mode', 'true');
    params.set('unit_id', unit?.id);
    params.set('unit_classification_id', unit?.room_classification_id);
    const url = `${pathname}?${params.toString()}`;
    router.push(url);
  };

  const onManageButtonClick = (unit) => () => {
    if (status === 'Listed') handleOnManageUnit(unit);
    else if (status === 'Pending')
      toast.warning(t('great_job_you_will_be_able_to_manage_the_units_after_rentoor_team_approves'));
    else if (status === 'Draft')
      toast.warning(t('please_complete_the_classification_details_before_you_can_manage_units'));
  };

  return (
    <div>
      <div className="flex items-center justify-between gap-2">
        <h2 className="text-secondary-300 text-base font-bold">{t('units_details')}</h2>
        <ClassificationActionButtons {...{ status, name: classification_name, id: classification_id, propertyId }} />
      </div>

      <div className="mt-4 flex w-full flex-col rounded-[12px] border border-gray-50">
        {units?.map((unit, index) => (
          <>
            <div className="m-3 flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex flex-col gap-x-11">
                  <p className="text-black-300">
                    {t('unit_no')} {index + 1}
                  </p>
                  <p className="text-gray">#{unit?.id}</p>
                </div>
                <div className="text-black-300 border-input-bg-300 ms-11 flex min-w-20 items-center justify-center rounded-full border bg-gray-50 p-4">
                  {unit?.sub_name}
                </div>
              </div>

              <Button
                variant="ghost"
                className="text-secondary-200  gap-x-2"
                size="sm"
                onClick={onManageButtonClick(unit)}>
                <IconWrapper name="Setting2" size={16} />
                {t('manage')}
              </Button>
            </div>
            {index !== units.length - 1 && <div className="h-px w-full bg-gray-50"></div>}
          </>
        ))}
      </div>
    </div>
  );
};

export default TabContent;
