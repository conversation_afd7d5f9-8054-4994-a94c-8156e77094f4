import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useGetRoleAccounts } from '@/queries/host-pages/administrative-accounts';
import { useFormContext } from 'react-hook-form';

const RoleList = () => {
  const { watch, setValue } = useFormContext();
  const { data, isPending } = useGetRoleAccounts();

  const role_id = watch('role_id');
  // Handle value change when a new property manager is selected
  const onValueChange = (selectedId: string) => {
    setValue('role_id', selectedId); // Update the form state
  };

  const roles = data?.data.data.slice(0, 2) || [];

  // Find the selected manager's name for displaying in SelectValue
  const selectedRole = roles.find((item) => `${item.id}` === `${role_id}`);
  const roles_selected_name = selectedRole?.display_name || '';

  if (isPending)
    return (
      <div className="mb-4">
        <Skeleton className="h-12 w-full bg-gray-100" />
      </div>
    );

  return (
    <div className="mb-4 w-full">
      <Select onValueChange={onValueChange}>
        <SelectTrigger className="w-full text-center">
          <SelectValue placeholder={roles_selected_name && <span>{roles_selected_name}</span>} />
        </SelectTrigger>

        <SelectContent>
          <SelectGroup>
            {roles.map(({ display_name, id }) => (
              <SelectItem value={`${id}`} key={id}>
                {display_name}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default RoleList;
