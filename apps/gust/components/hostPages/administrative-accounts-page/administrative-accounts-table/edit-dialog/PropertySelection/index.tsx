import { useScopedI18n } from '@/lib/i18n/client-translator';
import SelectedProperties from './SelectedProperties';
import PropertiesList from './PropertiesList';
import { useFormContext } from 'react-hook-form';

const PropertiesSelection = () => {
  const t = useScopedI18n('hostPages.administrative_accounts_page');

  const { watch } = useFormContext();
  const isPropertyManager = watch('role_id') === '15';

  return (
    <div>
      <h4 className="text-md font=bold text-secondary mb-3">{t('properties')}</h4>
      <p className="text-md text-gray mb-3">{isPropertyManager ? t('instruction') : t('all_properties')}</p>
      {isPropertyManager && (
        <>
          <PropertiesList />
          <SelectedProperties />
        </>
      )}
    </div>
  );
};

export default PropertiesSelection;
