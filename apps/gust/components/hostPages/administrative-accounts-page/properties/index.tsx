'use client';

import { cn } from '@/lib/utils';
import styles from './styles.module.css';
import TabListTriggers from '@/components/common/tab-list-triggers';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useDeletePropertyManager, useGetAdministrativeAccounts } from '@/queries/host-pages/administrative-accounts';
import { useEffect, useState } from 'react';
import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import SkeletonLoader from './skeleton-loader';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

const SectionWithTitle = ({ title, initialItems, assistant_director }) => {
  const t = useScopedI18n('hostPages.administrative_accounts_page');
  const { mutate, isPending } = useDeletePropertyManager();
  const [items, setItems] = useState(initialItems); // Local state to manage items
  const [loadingId, setLoadingId] = useState(null); // Track loading state for specific item

  const handleDelete = (manager_id, id) => {
    setLoadingId(id); // Set loading state for the current item
    mutate(
      { manager_id, id },
      {
        onSuccess: (data) => {
          // Remove the deleted item from the list
          setItems((prevItems) => prevItems.filter((item) => item.id !== id));
          toast.success(data.data.message);
          setLoadingId(null); // Reset loading state
        },
        onError: (error: any) => {
          toast.error(error?.response?.data.message);
          setLoadingId(null); // Reset loading state
        },
      }
    );
  };

  const hasItems = Boolean(items?.length);
  return (
    <div className="border-solod border-gray-75 flex flex-col gap-4 border-b pb-4">
      <div className="text-secondary-300 text-md font-bold">{title}</div>
      {hasItems ? (
        items?.map(({ first_name, last_name, id, manager_id }) => (
          <div className="text-black-400 box-border flex w-1/2 min-w-[400px] items-center justify-between rounded-[16px] border border-solid border-gray-50 px-4 py-3 ">
            <span className="truncate">{`${first_name} ${last_name}`}</span>

            {!assistant_director && (
              <Button
                loading={loadingId === id && isPending} // Show loading only for this button
                disabled={loadingId === id && isPending} // Disable the button while loading
                type="button"
                variant="link"
                color="primary"
                className="h-9 gap-[6px]"
                onClick={() => handleDelete(manager_id, id)}>
                <IconWrapper name="Trash" size={16} />
                <span className="text-md">{t('delete')}</span>
              </Button>
            )}
          </div>
        ))
      ) : (
        <div>{t('not_found')}</div>
      )}
    </div>
  );
};

const Properties = () => {
  const pageSize = 15;
  const [page, setPage] = useState<number>(1);
  const t = useScopedI18n('hostPages.administrative_accounts_page');
  const { refetch, isLoading, isFetching, data } = useGetAdministrativeAccounts({ page, pageSize });
  const router = useRouter();
  const [propertyId, setPropertyId] = useState('');

  const { data: { properties: listOfItems } = {} } = data?.data || {};
  const items = listOfItems?.map(({ id, name }) => ({ title: `${id}`, trans: name }));

  const linkTo = `/host/administrativeAccounts/addNewAdministrativeAccounts?specific=specific&&propertyId=${propertyId}`;
  const handleRedirectToAdd = () => router.push(linkTo);

  const handleSelectType = (title) => {
    setPropertyId(title);

    const selectedProperty = items.find((item) => item.title === title);
    const propertyName = selectedProperty?.trans || '';

    const searchParams = new URLSearchParams({
      propertyName,
    });

    router.push(`/host/administrativeAccounts?${searchParams.toString()}`);
  };
  useEffect(() => {
    if (data?.data?.data) refetch();
  }, []);

  useEffect(() => {
    if (items) {
      setPropertyId(items[0].title);
      handleSelectType(items[0].title);
    }
  }, []);

  if (isLoading || isFetching) return <SkeletonLoader />;

  return (
    <Tabs
      orientation="vertical"
      defaultValue={items?.[0]?.title}
      className={cn('flex h-[63vh]', styles.propertiesContainer)}>
      <div
        className={
          'h-full max-h-full overflow-y-auto overflow-x-hidden border-e-[1px] border-solid border-gray-50 px-4 py-8'
        }>
        <TabListTriggers
          items={items}
          handleSelectType={handleSelectType}
          vertical
          containerClassName="!shadow-none !gap-3 !h-full justify-normal m-0"
          className="data-[state=active]:text-secondary-300 data-[state=active]:border-primary-300 text-secondary-300 box-border w-full shadow-lg data-[state=active]:border-[1px]  data-[state=active]:border-solid data-[state=active]:bg-[#fff] data-[state=active]:font-normal data-[state=active]:shadow-none"
        />
      </div>

      <div className="h-max max-h-full flex-1 overflow-y-auto overflow-x-hidden px-6">
        {listOfItems?.map(({ id, assistantDirector, propertyManager }) => (
          <TabsContent className="flex flex-col gap-4 bg-transparent " value={`${id}`}>
            <SectionWithTitle
              assistant_director={true}
              initialItems={assistantDirector}
              title={t('assistant_director')}
            />
            <SectionWithTitle assistant_director={false} initialItems={propertyManager} title={t('property_manager')} />
          </TabsContent>
        ))}

        <Button onClick={handleRedirectToAdd} variant="link" className="text-secondary-300 text-md gap-2">
          <IconWrapper name="AddCircle" size={16} />
          {t('add_an_administrative_account')}
        </Button>
      </div>
    </Tabs>
  );
};

export default Properties;
