'use client';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import TabsHeader from './tabs-header';
import { Card } from '@/components/ui/card';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import ActiveListTable from './active-list-table';
import PendingListTable from './pending-list-table';
import DraftListTable from './draft-list-table';
import { useSearchParams } from 'next/navigation';

const MyPropertiesPage = () => {
  const t = useScopedI18n('hostPages');
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('activeTab') || 'Listed';

  return (
    <div className="min-h-full px-6 py-8">
      <Card className="gap-4 p-6">
        <div className="text-secondary-300 text-4xl font-bold"> {t('my_properties')}</div>
        <Tabs value={activeTab} className="min-h-full">
          <TabsHeader />

          <TabsContent className="bg-transparent" value="Listed">
            <ActiveListTable />
          </TabsContent>

          <TabsContent className="bg-transparent" value="Pending">
            <PendingListTable />
          </TabsContent>

          <TabsContent className="bg-transparent" value="Draft">
            <DraftListTable />
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default MyPropertiesPage;
