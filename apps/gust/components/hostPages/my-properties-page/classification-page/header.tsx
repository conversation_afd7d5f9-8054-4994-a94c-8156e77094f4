import IconWrapper from '@/components/icons';
import { Button } from '@/components/ui/button';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import Link from 'next/link';
import { classificationModeTypes } from '../../add-new-property/types';
import { usePathname } from 'next/navigation';
import AcceptDialog from '../pending-list-table/actions-btns/UnderReviewHost/AcceptDialog';
import RejectDialog from '../pending-list-table/actions-btns/UnderReviewHost/RejectDialog';
import { EditBtn } from '../../common';
import { useChangeQuery } from '../utils';

const Header = ({ type, classificationId }) => {
  const t = useScopedI18n('hostPages.myPropertiesPage');
  const { paramsWithValues } = useChangeQuery();
  const pathname = usePathname();
  const propertyId = pathname.split('/').pop();
  const isListed = type === 'Listed';
  const isPending = type === 'Pending';
  const isUnderReviewHost = paramsWithValues?.wp_status === 'UnderReviewHost';

  return (
    <div className="item-center flex justify-between">
      <div className="text-secondary-300 text-4xl font-bold">{t('classifications')}</div>

      {isUnderReviewHost && isPending && (
        <div className="flex gap-3">
          <AcceptDialog
            {...{
              id: classificationId,
              classification_type: 'different_classifications',
              room_classification_id: null,
            }}
          />
          <RejectDialog id={classificationId} />
          <EditBtn href={`/host/myProperties/addNewProperty?propertyId=${classificationId}`} />
        </div>
      )}

      {isListed && (
        <Link
          href={`/host/myProperties/addNewProperty?propertyId=${propertyId}&modeType=${classificationModeTypes.addClassification}`}>
          <Button className="gap-3">
            <IconWrapper name="AddCircle" size={16} />
            {t('add_new_classification')}
          </Button>
        </Link>
      )}
    </div>
  );
};

export default Header;
