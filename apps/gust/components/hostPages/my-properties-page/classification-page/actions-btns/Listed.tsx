'use client';
import { CopyBtn, DisplayBtn, EditBtn, ManageBtn } from '@/components/hostPages/common';
import { usePathname } from 'next/navigation';
import { classificationModeTypes } from '@/components/hostPages/add-new-property/types';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';

const Listed = ({ id }) => {
  const pathname = usePathname();
  const propertyId = pathname.split('/').pop();
  const { sendGAEvent } = useGAEvent();

  return (
    <>
      <DisplayBtn
        href={`${pathname}/classification?type=Listed&PropertyType=show&classification_id=${id}&classification_type=unit_from_different_classifications`}
      />

      <EditBtn
        href={`/host/myProperties/${propertyId}/classification?type=Listed&PropertyType=edit&classification_id=${id}&classification_type=unit_from_different_classifications`}
      />

      <ManageBtn
        href={`${pathname}/classification?type=Listed&PropertyType=manage&classification_id=${id}&classification_type=unit_from_different_classifications`}
      />

      <CopyBtn
        href={`/host/myProperties/addNewProperty?propertyId=${propertyId}&copiedClassificationId=${id}&modeType=${classificationModeTypes.copyClassification}`}
        onClick={() => sendGAEvent({ action: 'property_copied' })}
      />
    </>
  );
};

export default Listed;
