'use client';
import { usePathname } from 'next/navigation';
import { CopyBtn, EditBtn, ManageBtn, ViewBtn } from '../../common';
import { propertyModeTypes } from '../../add-new-property/types';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';

const ActionsBtns = ({ classification_type, id, room_classification_id }) => {
  const pathname = usePathname();
  const isMultiClassification = classification_type === 'different_classifications';
  const { sendGAEvent } = useGAEvent();

  return (
    <div className="flex gap-3">
      <ViewBtn
        href={
          isMultiClassification
            ? `${pathname}/${id}?type=Listed`
            : `${pathname}/${id}/classification?type=Listed&PropertyType=show&classification_id=${room_classification_id}&classification_type=${classification_type}`
        }
      />

      <EditBtn
        href={
          room_classification_id
            ? `${pathname}/${id}/classification?type=Listed&PropertyType=edit&classification_id=${room_classification_id}&classification_type=${classification_type}`
            : `${pathname}/${id}/classification?type=Listed&PropertyType=edit&classification_type=${classification_type}`
        }
      />

      <ManageBtn
        href={
          room_classification_id
            ? `${pathname}/${id}/classification?type=Listed&PropertyType=manage&classification_type=${classification_type}&classification_id=${room_classification_id}`
            : `${pathname}/${id}/classification?type=Listed&PropertyType=manage&classification_type=${classification_type}`
        }
      />

      <CopyBtn
        href={`${pathname}/addNewProperty?modeType=${propertyModeTypes.copyProperty}&copiedPropertyId=${id}`}
        onClick={() => sendGAEvent({ action: 'property_copied' })}
      />
    </div>
  );
};

export default ActionsBtns;
