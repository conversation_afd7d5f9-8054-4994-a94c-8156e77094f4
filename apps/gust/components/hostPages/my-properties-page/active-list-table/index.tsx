import { Table, TableBody, TableHead, TableHeader, TableRow, TableCell } from '@/components/ui/table';
import { useGetMyPropertiesDetails } from '@/queries/host-pages/my-properties';
import Paginator from '@/components/common/paginator';
import { useEffect } from 'react';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import ActionsBtns from './actions-btns';
import TableSkeletonLoader from '@/components/common/skeleton-loaders/table';
import PropertyItem from '../property-item';
import { formatDateTime, useChangeQuery } from '../utils';
import { EmptyTableFallback } from '../../common';
const pageSize = 15;
const ActiveListTable = () => {
  const { refetch, isLoading, isFetching, data } = useGetMyPropertiesDetails({ pageSize });
  const t = useScopedI18n('hostPages.myPropertiesPage');

  const {
    onChangeQuery,
    paramsWithValues: { page: currentPage },
  } = useChangeQuery();

  const page = Number(currentPage || 1);

  const { pages, data: listOfItems } = data?.data || {};
  const hasData = Boolean(listOfItems?.length);
  const onChangePage = (page) => onChangeQuery({ page });

  useEffect(() => {
    if (data?.data?.data) refetch();
  }, []);

  if (isLoading || isFetching) return <TableSkeletonLoader rows={pageSize} />;

  return (
    <div>
      <Table className="table-auto ">
        <TableHeader>
          <TableRow className="hover:bg-gray-50">
            <TableHead>#</TableHead>
            <TableHead>{t('property')}</TableHead>
            <TableHead>{t('location')}</TableHead>
            <TableHead>{t('last_update')}</TableHead>
            <TableHead>{t('actions')}</TableHead>
          </TableRow>
        </TableHeader>

        {hasData && (
          <TableBody>
            {listOfItems.map(
              ({ original_image, main_name, classification_type, room_classification_id, ...row }, index) => {
                const itemNumber = (page - 1) * pageSize + (index + 1);
                const date = formatDateTime(row.update_at);
                const location = [row.city, row.address_line_1].filter(Boolean).join(' - ');

                return (
                  <TableRow key={room_classification_id || row.id}>
                    <TableCell>{itemNumber}</TableCell>

                    <TableCell>
                      <PropertyItem {...{ original_image, main_name, classification_type }} />
                    </TableCell>

                    <TableCell className="max-w-40 truncate" title={location}>
                      {location}
                    </TableCell>

                    <TableCell>{date}</TableCell>

                    <TableCell>
                      <ActionsBtns
                        classification_type={classification_type}
                        id={row.id}
                        room_classification_id={room_classification_id}
                      />
                    </TableCell>
                  </TableRow>
                );
              }
            )}
          </TableBody>
        )}
      </Table>

      {!hasData && <EmptyTableFallback className="min-h-[50vh]" Text={t('noActiveProperties')} />}

      <Paginator
        paginator={pages}
        page={page}
        setPage={onChangePage}
        className="border-gray-75 mt-6 justify-start border-t border-solid pt-4"
      />
    </div>
  );
};

export default ActiveListTable;
