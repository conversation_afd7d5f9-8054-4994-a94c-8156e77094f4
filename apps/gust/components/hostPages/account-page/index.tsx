'use client';
import TabListTriggers from '@/components/common/tab-list-triggers';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import Profile from './profile';
import AccountType from './account-type';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import Payouts from './payouts';
import useTabWithSearchParam from '@/hooks/use-tab-with-search-params';
import DeleteAccount from './delete_account';

const AccountPage = () => {
  const t = useScopedI18n('hostPages.accountPage');
  const { activeTab, handleTabChange } = useTabWithSearchParam('profile');

  const items = [
    { title: 'profile', trans: t('profile') },
    { title: 'accountType', trans: t('account_type') },
    { title: 'payouts', trans: t('payouts') },
    { title: 'delete_account', trans: t('delete_account') },
  ];
  return (
    <div className="h-full">
      <Tabs
        orientation="vertical"
        defaultValue={activeTab}
        value={activeTab}
        onValueChange={(tabName) => handleTabChange(tabName)}
        className="flex h-full">
        <div className={'border-gray-75 min-h-full border-e-[1px] border-solid px-4 py-8'}>
          <TabListTriggers
            items={items}
            vertical
            containerClassName="!shadow-none !gap-3"
            className="data-[state=active]:text-secondary-300 data-[state=active]:border-primary-300 text-secondary-300 box-border w-full shadow-lg data-[state=active]:border-[1px]  data-[state=active]:border-solid data-[state=active]:bg-[#fff] data-[state=active]:font-normal data-[state=active]:shadow-none"
          />
        </div>

        <div className="h-full flex-1 overflow-auto px-10 py-8">
          <TabsContent className="bg-transparent" value="profile">
            <Profile />
          </TabsContent>

          <TabsContent className="bg-transparent" value="accountType">
            <AccountType />
          </TabsContent>
          <TabsContent className="bg-transparent" value="payouts">
            <Payouts />
          </TabsContent>
          <TabsContent className="bg-transparent" value="delete_account">
            <DeleteAccount />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default AccountPage;
