import IconWrapper from '@/components/icons';
import { Card } from '@/components/ui/card';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import React from 'react';

const CardItem = ({ icon, title, amount }) => {
  const t = useScopedI18n('wallet');
  const currentLang = useCurrentLocale();

  return (
    <Card className="bg-white-50 flex min-h-[80px] flex-1 flex-row gap-5 p-3">
      <div className="flex h-14 w-14 shrink-0 items-center justify-center rounded-[12px] bg-[#EBEFF3]">
        <IconWrapper name={icon} size={32} className="text-secondary-300" />
      </div>
      <div className="flex flex-1 flex-col gap-3">
        <h2 className="text-secondary-300 text-[14px] font-bold">{title}</h2>
        <p className="text-primary-300 text-[18px] font-bold">
          {amount}{' '}
          {currentLang === 'ar' ? (
            <IconWrapper name="Saudi_Riyal_Symbol" className="fill-primary" size={15} />
          ) : (
            t('sar')
          )}
        </p>
      </div>
    </Card>
  );
};

export default CardItem;
