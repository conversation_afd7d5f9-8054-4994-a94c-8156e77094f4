import IconWrapper, { icons } from '@/components/icons';
import { Button, ButtonProps } from '@/components/ui/button';
import Loader from '@/components/ui/loader';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface IMainActionBtn extends ButtonProps {
  href?: string;
  label: string;
  iconName?: keyof typeof icons | '';
  iconSize?: number;
  className?: string;
  link?: boolean;
}

const MainActionBtn = ({
  href = '',
  label,
  iconName,
  iconSize = 16,
  variant = 'outline',
  className,
  loading = false,
  disabled = false,
  ...props
}: IMainActionBtn) => {
  const btnClasses = 'action-button-host-table';

  const baseButton = (
    <Button variant={variant} className={cn(btnClasses, className)} {...props} disabled={loading || disabled}>
      {loading ? (
        <Loader />
      ) : (
        Boolean(iconName) && <IconWrapper name={iconName as keyof typeof icons} size={iconSize} />
      )}
      {label}
    </Button>
  );

  return Boolean(href) ? <Link href={href}>{baseButton}</Link> : baseButton;
};

export const CopyBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');

  return <MainActionBtn label={t(props.link ? 'copy_link' : 'copy')} iconName="Copy" {...props} />;
};

export const EditBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('edit')} iconName="Edit2" {...props} />;
};

export const ManageBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('management')} iconName="Setting2" {...props} />;
};

export const RejectionReasonsBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('rejection_reasons')} iconName="NoteRemove" {...props} />;
};

export const PropertyDetailsBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('property_details')} iconName="Notepad2" {...props} />;
};

export const ViewBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('view')} iconName="Eye" {...props} />;
};

export const SetWebsiteBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('set_website')} iconName="GlobalEdit" {...props} />;
};

export const DeleteBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('delete')} iconName="Trash" {...props} />;
};

export const RecoveryBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('recovery')} iconName="Sync" {...props} />;
};

export const PermanentlyDeleteBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('permanently_delete')} {...props} />;
};

export const ConversationBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('conversation')} {...props} />;
};

export const AcceptBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('accept')} iconName="TickSquare" {...props} />;
};

export const RejectBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('reject')} iconName="CloseSquare" {...props} />;
};

export const ReservationDeclineBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('reservation_decline')} {...props} />;
};

export const DisplayBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('display')} iconName="Eye" {...props} />;
};

export const PrintBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('print')} iconName="Printer" {...props} />;
};

export const DownloadBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('download')} iconName="DocumentDownload" {...props} />;
};

export const ShareBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('share')} iconName="ShareIcon" {...props} />;
};

export const ResendBtn = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return <MainActionBtn label={t('resend')} iconName="Setting2" {...props} />;
};

export const MakeDefault = (props: Partial<IMainActionBtn>) => {
  const t = useScopedI18n('common');
  return (
    <MainActionBtn
      label={!props.disabled ? t('MakeDefault') : t('done_make_default')}
      disabled={props.disabled}
      iconName={!props.disabled ? 'TickSquare' : undefined}
      {...props}
    />
  );
};

export default MainActionBtn;
