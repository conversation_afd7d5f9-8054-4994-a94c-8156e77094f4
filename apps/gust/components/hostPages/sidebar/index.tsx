import { usePathname } from 'next/navigation';
import Link from 'next/link';
import IconWrapper, { icons } from '@/components/icons';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { cn } from '@/lib/utils';
import styles from './styles.module.css';
import { useUserStore } from '@/store/useUserStore';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';
import useNotificationsCount from '@/hooks/useNotificationsCountHandler';

const useSidebarOptions = () => {
  const t = useScopedI18n('hostPages');
  const { sendGAEvent } = useGAEvent();

  return [
    {
      label: t('dashboard'),
      url: '/',
      iconName: 'SmallLogo',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('my_properties'),
      url: '/myProperties',
      iconName: 'House',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('my_reservations'),
      url: '/myReservations',
      iconName: 'Calendar',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('inbox'),
      url: '/inbox',
      iconName: 'Message',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('account'),
      url: '/account',
      iconName: 'ProfileCircle',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('invoices'),
      url: '/invoices',
      iconName: 'Bill',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('administrative_accounts'),
      url: '/administrativeAccounts',
      iconName: 'NotificationStatus',
      permission: ['Owner', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('wallet'),
      url: '/wallet',
      iconName: 'EmptyWallet',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      accountType: ['Establishment', 'Individual'],
    },
    {
      label: t('websites_for_my_real_estate_properties'),
      url: '/myRentoor',
      iconName: 'GlobalSearch',
      permission: ['Owner', 'Property Manager', 'Assistant Director'],
      onClick: () => sendGAEvent({ action: 'my_rentoor_nav_clicked' }),
      accountType: ['Establishment'],
    },
  ];
};

const SidebarItem = ({
  isCollapsed,
  iconName,
  label,
  isActive,
  onClick,
  suffixNumber,
}: {
  isCollapsed: boolean;
  iconName: string;
  label: string;
  isActive: boolean;
  onClick?: () => void;
  suffixNumber?: number;
}) => {
  const itemContainerClassName = cn(
    styles.sidebarMenuItem,
    isActive && styles.sidebarActiveMenuItem,
    isCollapsed && styles.sidebarCollapsedMenuItem
  );

  return (
    <div className={itemContainerClassName} onClick={onClick}>
      <span>
        <IconWrapper name={iconName as keyof typeof icons} size={20} />
      </span>
      <span className="flex flex-1 justify-between">
        <span className={cn('text-[14px]', isCollapsed && 'opacity-0', styles.menuItemLabel)}>{label}</span>
        {Boolean(suffixNumber) && (
          <span className="text-primary-300 bg-primary-50 me-2 rounded-full px-2 py-0.5 text-sm">{suffixNumber}</span>
        )}
      </span>
    </div>
  );
};

const HostPagesSidebar = ({ isCollapsed, onToggleCollapse }) => {
  const t = useScopedI18n('hostPages');
  const sidebarOptions = useSidebarOptions();
  const { userData, userAccount } = useUserStore();
  const pathname = usePathname();
  const currentLocale = useCurrentLocale();
  const isEnLang = currentLocale === 'en';
  const { notificationsCount } = useNotificationsCount();

  const accountType = userAccount?.host_commercial_registration?.account_type || '';
  const role_type: any = userData?.role_type?.role_type || 'Owner';

  const filteredOptions = sidebarOptions.filter(
    (option) => option.permission.includes(role_type) && option.accountType.includes(accountType)
  );

  return (
    <aside className={cn(styles.sidebarContainer, isCollapsed && styles.collapsedSidebarContainer)}>
      <span onClick={onToggleCollapse} className={styles.toggleIconContainer}>
        <IconWrapper
          name="RightArrow"
          size="16"
          className={cn(!isCollapsed && isEnLang && 'rotate-180', isCollapsed && !isEnLang && 'rotate-[-180deg]')}
        />
      </span>

      <div className="flex min-w-11 flex-1 flex-col items-start gap-[8px] overflow-hidden">
        {filteredOptions.map(({ label, url, iconName, onClick }) => (
          <Link key={url} href={'/host' + url} className="w-full">
            <SidebarItem
              {...{ iconName, isCollapsed, label, pathname }}
              isActive={url === '/' ? pathname === `/${currentLocale}/host` : pathname.includes(url)}
              onClick={onClick}
              suffixNumber={url === '/inbox' ? notificationsCount : undefined}
            />
          </Link>
        ))}
      </div>

      <div title="Commin Soon">
        <SidebarItem iconName="Logout" isCollapsed={isCollapsed} label={t('logout')} isActive={false} />
      </div>
    </aside>
  );
};

export default HostPagesSidebar;
