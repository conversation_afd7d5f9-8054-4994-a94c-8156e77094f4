import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import Loader from '@/components/ui/loader';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'shadow-sm bg-primary text-primary-foreground hover:bg-primary-400 active:bg-primary-75 disabled:bg-gray-300',
        destructive: 'shadow-sm bg-failed text-failed-foreground hover:bg-failed-400',
        outline: 'border border-gray-75 bg-white-50 hover:bg-gray-50 hover:shadow-lg',
        secondary: 'shadow-sm bg-secondary text-secondary-foreground  hover:bg-secondary-400 active:bg-secondary-75',
        disabled: 'bg-gray-50 !text-gray-700 border !border-gray-200',
        gray: 'bg-gray-300 shadow-sm text-white-full hover:bg-gray-400 disabled:bg-gray-200',
        ghost: '',
        link: 'text-primary underline-offset-4 hover:underline !p-0',
        'outline-secondary': 'border border-gray-75 text-secondary hover:bg-gray-50 hover:shadow-lg',
        'input-icon':
          'bg-white-75 hover:border-primary-200 focus-visible:border-primary-200 md-text h-12 w-full justify-start gap-2 rounded-md border border-gray-50 !p-0 placeholder:text-gray-100 focus-visible:outline-none',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-12 px-8 rounded-md',
        icon: 'h-10 w-10',
        icon_sm: 'h-8 w-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
  asChild?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} disabled={loading} {...props}>
        <>
          {loading ? <Loader /> : null}

          {props.children}
        </>
      </Comp>
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
