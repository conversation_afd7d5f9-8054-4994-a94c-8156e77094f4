import { useScopedI18n } from '@/lib/i18n/client-translator';
import { TabsType } from '@/types/homepage';

export const useGetResevationTabs = (): TabsType[] => {
  const t = useScopedI18n('reserve');
  return [
    {
      title: 'now',
      trans: t('allowed'),
    },
    {
      title: 'finished',
      trans: t('finished'),
    },
  ];
};

export const useGetReservationRate = () => {
  const t = useScopedI18n('reserve');
  return [
    { key: 'accuracy', label: t('accuracy') },
    { key: 'cleanliness', label: t('cleanliness') },
    { key: 'checkin', label: t('checkin') },
    { key: 'location', label: t('location') },
    { key: 'communication', label: t('communication') },
    { key: 'service', label: t('service') },
  ];
};

export const useGetReservationStatus = (currentTab: string) => {
  const t = useScopedI18n('status');

  if (currentTab === 'now') {
    return [
      { id: 1, label: t('Accepted'), value: 'Accepted' },
      { id: 2, label: t('Pending'), value: 'Pending' },
      { id: 3, label: t('Pre-Accepted'), value: 'Pre-Accepted' },
      
    ]
  }
  if (currentTab === 'finished') {
    return [
      { id: 1, label: t('Cancelled'), value: 'Cancelled' },
      { id: 2, label: t('Declined'), value: 'Declined' },
      { id: 3, label: t('Expired'), value: 'Expired' },
      { id: 4, label: t('Completed'), value: 'Completed' },

    ]
  }
  return [];
};
