'use client';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import React, { FC, useEffect, useState } from 'react';
import { Drawer, DrawerClose, DrawerContent, DrawerFooter, DrawerHeader } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import { DialogClose } from '@/components/ui/dialog';
import InvoiceFrame from '@/public/assets/invoice_frame.png';
import { Separator } from '@/components/ui/separator';
import RentoorAR from '@/public/assets/rentoor_logo_ar.png';
import RentoorEN from '@/public/assets/rentoor_logo_en.png';
import { cn, getLayoutDirection } from '@/lib/utils';
import Loader from '@/components/ui/loader';
import { toast } from 'sonner';
import Cookies from 'js-cookie';
import Image_Backend from '@/components/common/Image_backend';
import { useUserStore } from '@/stores/user-store';
import { HOST_URL } from '../../../../../../packages/url-config';

const SuccessDialog = () => {
  const cot = useScopedI18n('common');
  const locale = useCurrentLocale();
  const t = useScopedI18n('reserve');
  const hst = useScopedI18n('homepage.search');

  const currT = useScopedI18n('currency');
  const dir = getLayoutDirection(locale);
  const { userToken } = useUserStore();

  // const router = useRouter();
  const [invoiceData, setInvoiceData] = useState<any | null>(null);

  // Fetch `invoice` from sessionStorage on client side
  const invoice = Cookies.get('invoice');

  useEffect(() => {
    if (invoice) {
      setInvoiceData(JSON.parse(invoice));
    }
  }, []);

  const handleStep = () => {
    if (userToken) {
      window.parent.location = `${process.env.NEXT_PUBLIC_GUEST_URL}/${locale}/reservation?type=now&reservation_id=${invoiceData?.id}&token=${encodeURIComponent(userToken)}`;
    } else {
      window.parent.location = `${process.env.NEXT_PUBLIC_GUEST_URL}/${locale}/reservation?type=now&reservation_id=${invoiceData?.id}`;
    }
  };
  const handleCopyInvoiceLink = () => {
    const invoiceLink = `${HOST_URL}/${locale}/invoice-download/${invoiceData?.invoice?.id}`;
    navigator.clipboard
      .writeText(invoiceLink)
      .then(() => {
        toast.success(cot('link_copied'));
      })
      .catch((err) => {
        console.error('Failed to copy invoice link: ', err);
      });
  };
  if (!invoiceData) {
    // Show a loading state or fallback UI until the invoice data is fetched
    return (
      <div className="flex h-[75vh] w-full items-center justify-center">
        <Loader size={'30px'} />
      </div>
    );
  }
  const isPartialPayment =
    invoiceData?.invoice?.paid_amount > 0 && invoiceData?.invoice?.paid_amount < invoiceData?.invoice?.total_price;

  return (
    <Drawer open={true} direction="bottom">
      <DrawerContent className="w-full bg-gray-50">
        <DrawerHeader className="px-6 py-3">
          <Button onClick={handleCopyInvoiceLink} variant="ghost" className="border-secondary-200 h-7 w-7 border p-0 ">
            <IconWrapper name="Share" className="text-secondary" size={15} />
          </Button>
          <DrawerClose onClick={handleStep} asChild>
            <DialogClose />
          </DrawerClose>
        </DrawerHeader>
        <>
          <div className="bg-white-50 rounded-t-xs mx-6 -mb-3 flex flex-col  items-center justify-center gap-4 p-2">
            <Image_Backend
              src={locale === 'ar' ? RentoorAR : RentoorEN}
              height={20}
              width={106}
              alt="rentoor logo"
              className="mt-4"
            />
            <Separator orientation="horizontal" />
          </div>
          <div
            className={`scrollable-content mx-6 h-[75vh] max-h-[150vh] min-h-[150px]  overflow-auto bg-bottom bg-no-repeat px-5 shadow-xl`}
            style={{
              backgroundImage: `url(${InvoiceFrame.src})`,
              backgroundSize: '100% 100%',
            }}>
            <div className="my-4 flex flex-col items-center justify-center gap-y-2">
              <div className="bg-success flex h-14 w-14 items-center justify-center rounded-full ">
                <IconWrapper name="CardTick" className="text-white " size={26} />
              </div>
              <p className="text-secondary lg-text font-bold">{t('payment_successful')}</p>
              <p className="md-text text-gray-400">{t('payment_successful_desc')}</p>
            </div>
            <Separator orientation="horizontal" className="border-gray/20 mb-4 border-b border-dashed bg-transparent" />
            <div className="flex flex-col gap-y-2 pb-7">
              <InvoiceDetailBlock
                title={t('property_name')}
                value={invoiceData?.classification_name}
                className={`max-h-6 max-w-fit overflow-hidden text-ellipsis ${dir === 'rtl' ? 'text-left' : 'text-right'}`}
              />
              <InvoiceDetailBlock title={hst('arrival_date')} value={invoiceData?.invoice.checkin} />
              <InvoiceDetailBlock title={hst('departure_date')} value={invoiceData?.invoice.checkout} />
              <InvoiceDetailBlock title={hst('number_guests')} value={String(invoiceData?.invoice?.number_of_guests)} />
              <InvoiceDetailBlock title={t('nights')} value={String(invoiceData?.invoice?.total_nights)} />
              <InvoiceDetailBlock title={t('payment_method')} value={t('credit_card')} />

              <Separator
                orientation="horizontal"
                className="border-gray/20 mt-2 border-b border-dashed bg-transparent"
              />
              <InvoiceDetailBlock
                title={t('night_price', {
                  nights: invoiceData?.invoice?.total_nights,
                  price: Number(invoiceData?.invoice?.total_price).toFixed(2),
                  curr: currT('sar'),
                })}
                value={
                  <>
                    {(
                      (invoiceData?.invoice?.total_nights ?? 0) * (invoiceData?.invoice?.per_night_price ?? 0)
                    ).toString()}{' '}
                    {locale === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400 " size={15} />
                    ) : (
                      currT('sar')
                    )}
                  </>
                }
              />
              <InvoiceDetailBlock
                title={t('service_fee')}
                value={
                  <>
                    {(invoiceData?.invoice?.service_fee ?? 0).toString()}{' '}
                    {locale === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                    ) : (
                      currT('sar')
                    )}
                  </>
                }
              />
              <InvoiceDetailBlock
                title={t('tax_fee')}
                value={
                  <>
                    {(invoiceData?.invoice?.tax ?? 0).toString()}{' '}
                    {locale === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400 " size={15} />
                    ) : (
                      currT('sar')
                    )}
                  </>
                }
              />
              <InvoiceDetailBlock
                title={t('total_pay')}
                value={
                  <>
                    {(invoiceData?.invoice?.total_price ?? 0).toString()}{' '}
                    {locale === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-secondary" size={15} />
                    ) : (
                      currT('sar')
                    )}
                  </>
                }
                className="text-secondary font-bold"
              />
              {isPartialPayment && (
                <InvoiceDetailBlock
                  title={t('amount_already_paid')}
                  value={
                    <>
                      {(invoiceData?.invoice?.paid_amount ?? 0).toString()}{' '}
                      {locale === 'ar' ? (
                        <IconWrapper name="Saudi_Riyal_Symbol" className="fill-secondary" size={15} />
                      ) : (
                        currT('sar')
                      )}
                    </>
                  }
                  className="text-secondary font-bold"
                />
              )}
              {isPartialPayment && (
                <InvoiceDetailBlock
                  title={t('remaining_amount')}
                  value={
                    <>
                      {(invoiceData?.invoice?.total_price - invoiceData?.invoice?.paid_amount).toString()}{' '}
                      {locale === 'ar' ? (
                        <IconWrapper name="Saudi_Riyal_Symbol" className="fill-secondary" size={15} />
                      ) : (
                        currT('sar')
                      )}
                    </>
                  }
                  className="text-secondary font-bold"
                />
              )}
            </div>
          </div>
        </>
        <DrawerFooter>
          <Button variant="default" className="text-white-full" onClick={handleStep}>
            {t('navigate_booking_details')}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

const InvoiceDetailBlock: FC<{ title: string; value: any | undefined; className?: string }> = ({
  title,
  value,
  className,
}) => {
  const t = useScopedI18n('common');

  return (
    <div className="flex items-center justify-between gap-4">
      <p className="md-text text-secondary-200 text-left font-bold">{title}</p>
      <p className={cn(`md-text text-gray-400`, className)}>{value ?? t('not_applicable')}</p>
    </div>
  );
};

export default SuccessDialog;
